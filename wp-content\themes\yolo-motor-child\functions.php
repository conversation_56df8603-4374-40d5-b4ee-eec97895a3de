<?php

/**
 * Theme functions for YOLO Framework.
 * This file include the framework functions, it should remain intact between themes.
 * For theme specified functions, see file functions-<theme name>.php
 *
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2016, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
 */
// define( 'YOLO_SCRIPT_DEBUG', true); // use for developer only - delete or comment when finish
// ini_set('xdebug.max_nesting_level', 500); // Fix xdebug Fatal error: Maximum function nesting level of '100' reached, aborting! (need change in php.ini and delete here)

$uploaded_file_name = '';

// Define missing parent theme function to prevent errors
if ( ! function_exists( 'yolo_include_footer_id' ) ) {
    function yolo_include_footer_id() {
        return '';
    }
}

// Include SAP pricing functions
require_once get_stylesheet_directory() . '/functions-sap-pricing.php';

add_action('init', function () {
    error_log('user is not logged in for class&&');
    if (is_user_logged_in()) {
        error_log('user is logged in for class&&');
        // Include your custom product class
        require_once get_stylesheet_directory() . '/woocommerce/includes/class-my-custom-product.php';
    }
});

add_filter('woocommerce_product_class', function ($classname, $product_type, $product_id) {
    error_log('classes loading &&');
    // Replace WC_Product with My_Custom_Product
    if ($classname === 'WC_Product_Simple') {
        error_log('loaded&&');
        return 'WC_Custom_Product';
    }
    return $classname;
}, 10, 3);

function yolo_enqueue_parent_styles()
{
    wp_enqueue_style('child-style', get_stylesheet_directory_uri() . '/style.css');
    wp_enqueue_style('select2-style', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
    wp_enqueue_style('datatables-css', 'https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css');
    wp_enqueue_script('datatables-js', 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js', array('jquery'), null, true);
    wp_enqueue_script('select2-js', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), null, true);
}

add_action('wp_enqueue_scripts', 'yolo_enqueue_parent_styles', 9999);
add_filter('woocommerce_admin_disabled', '__return_true');
add_filter('woocommerce_marketing_menu_items', '__return_empty_array');
add_filter('woocommerce_admin_features', function ($features) {
    $marketing_key = array_search('marketing', $features);
    if (false !== $marketing_key) {
        unset($features[$marketing_key]);
    }
    return $features;
});
add_filter('woocommerce_admin_daily', '__return_false');
add_filter('woocommerce_admin_hourly', '__return_false');

add_action('wp_head', 'define_ajax_url');
function define_ajax_url()
{
    ?>
    <script type="text/javascript">
        var ajaxurl = "<?php echo admin_url('admin-ajax.php'); ?>";
    </script>
    <?php
}

add_action('wp_ajax_fetch_products', 'fetch_products_ajax');
add_action('wp_ajax_nopriv_fetch_products', 'fetch_products_ajax');
function fetch_products_ajax()
{
    error_log("fetch_products_ajax called");
    global $total_get_data;
    $search_value = isset($_POST['search']['value']) ? sanitize_text_field($_POST['search']['value']) : '';
    $in_stock_only = isset($_POST['inStockOnly']) && $_POST['inStockOnly'] === 'true';
    $sort_by = isset($_POST['sortBy']) ? sanitize_text_field($_POST['sortBy']) : 'default';
    $suffix = get_stock_suffix();
    $meta_query = [];

    // Updated "In Stock Only" filter to use customer-based stock fields
    if ($in_stock_only) {
        // Get customer location for stock determination
        $main_user_id = get_current_user_id();
        $company_code = get_user_meta($main_user_id, '_companycode', true);
        $is_us_customer = ($company_code == "3090");

        $stock_meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';

        $meta_query[] = [
            'key' => $stock_meta_key,
            'value' => 0,
            'compare' => '>',
        ];

        // Old filter logic - commented for future reference
        // $meta_query[] = [
        //     'key' => '_stock_status',
        //     'value' => 'instock',
        //     'compare' => '=',
        // ];
    }

    $price_code_filtering_ids = [];
    $ce_approved_filter = [];

    foreach ($_POST['customFields'] as $key => $values) {
        if (!empty($values)) {
            if ($key == 'ce_approved') {
                $ce_approved_filter = $values;
            } elseif ($key !== 'usd_price_code') { // ignore deprecated price code filter
                $meta_query[] = [
                    'key' => '_' . $key,
                    'value' => $values,
                    'compare' => 'IN',
                ];
            }
        }
    }

    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    // Price code filtering removed from products table
    $get_data = get_product_for_customer_by_id($main_user_id, [], $ce_approved_filter); // Retrieve filtered product IDs without price code filter

    // var_dump($get_data);
    // exit;
    $_SESSION['total_get_data'] = $get_data;
    $filtered_ids_new = $get_data['new_price_products'];
    $filtered_ids_price = $get_data['allowed_products'];
    $total_price_codes = $get_data['allowed_price_codes'];
    $filtered_ids = array_merge($filtered_ids_new, $filtered_ids_price);

    $args = [];
    $query = [];
    $products = [];

    if (empty($filtered_ids)) {
        $query = []; // Return an empty array or handle accordingly
    } else {
        // Handle DataTables sorting
        $orderby = 'post__in'; // Default ordering
        $order = 'ASC';
        $meta_key = '';

        if (isset($_POST['order']) && !empty($_POST['order'])) {
            $order_column = intval($_POST['order'][0]['column']);
            $order_dir = sanitize_text_field($_POST['order'][0]['dir']);

            // Map DataTables columns to WordPress orderby values and meta keys
            $column_config = [
                1 => ['orderby' => 'title', 'meta_key' => ''],
                2 => ['orderby' => 'meta_value_num', 'meta_key' => '_stock_us'], // Will be dynamic based on customer
                3 => ['orderby' => 'meta_value_num', 'meta_key' => '_regular_price'],
                4 => ['orderby' => 'post__in', 'meta_key' => ''], // Net price - not sortable, use default
                5 => ['orderby' => 'meta_value', 'meta_key' => '_brand'],
                6 => ['orderby' => 'meta_value', 'meta_key' => '_product_line'],
                7 => ['orderby' => 'meta_value', 'meta_key' => '_product_family'],
                8 => ['orderby' => 'meta_value', 'meta_key' => '_model_size'],
                9 => ['orderby' => 'meta_value', 'meta_key' => '_ce_approved']
            ];

            if (isset($column_config[$order_column])) {
                $config = $column_config[$order_column];
                $orderby = $config['orderby'];
                $meta_key = $config['meta_key'];
                $order = strtoupper($order_dir);

                // Handle stock column - use customer-specific stock field
                if ($order_column == 2) {
                    $main_user_id = get_current_user_id();
                    $company_code = get_user_meta($main_user_id, '_companycode', true);
                    $is_us_customer = ($company_code == "3090");
                    $meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';
                }
            }
        }

        $args = [
            'post__in' => $filtered_ids,
            'post_type' => 'product',
            'posts_per_page' => isset($_POST['length']) ? intval($_POST['length']) : 10,
            'paged' => isset($_POST['start']) ? (intval($_POST['start']) / intval($_POST['length']) + 1) : 1,
            's' => $search_value,
            'meta_query' => $meta_query,
            'orderby' => $orderby,
            'order' => $order
        ];

        // Add meta_key if we're sorting by meta values
        if (!empty($meta_key)) {
            $args['meta_key'] = $meta_key;
        }

        $query = new WP_Query($args);
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                global $product;

                // Price code filtering removed for products table view
                // $check_price_code = get_filtered_product_price_code($product->get_SKU(), $price_code_filtering_ids, $ce_approved_filter);
                // if (!$check_price_code) {
                //     continue;
                // }
                $num = rand(0, 500);
                $number = ceil($num / 10) * 20;

                $description = get_post_meta($product->get_id(), '_description', true);
                // var_dump($description);
                // exit;
                $products[] = [
                    'quantity' => '<div class="flex aic flex-column mx-auto" ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'data-tooltip="Not Available from Stock"') .  */'>
                                    <div class="single-shop-item flex aic">
                                        <div class="custom-check ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'disabledd') .  */'">
                                            <input type="checkbox" name="products[]" value="' . get_the_ID() . '" id="product_id_' . get_the_ID() . '" />
                                            <label for="product_id_' . get_the_ID() . '"></label>
                                        </div>
                                        <div class="quantity-wrap ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'disabledd') .  */'">
                                            <span class="minus">-</span>
                                            <input type="text" name="quantity" value="1">
                                            <span class="plus">+</span>
                                        </div>
                                    </div>
                                    <div class="custom-button w100 text-center">
                                        <button class="orange-bg white w100 no-border py-1 add_to_cart_button ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'disabledd') .  */'" data-product-id="' . get_the_ID() . '" data-quantity="1">Add To Cart</button>
                                    </div>
                                   </div>',
                    'title' => '<a href="' . get_permalink($product->get_id()) . '">
                                        <div class="flex aic">
                                           <span class="table-product-image">' . (has_post_thumbnail() ? get_the_post_thumbnail(get_the_ID(), 'thumbnail') : "No Photo Available") . '</span>
                                           <div class="ml-1">
                                               <p class="table-product-title mb-0">' . get_the_title() . '</p>
                                               <p class="table-product-description mb-0">' . $description . '</p>
                                           </div>
                                       </div>
                                    </a>', // get_the_title(),
                    // 'stock'   => '<p class="table-product-stock mb-0"><span class="orange">' . get_post_meta(get_the_ID(), '_stock', true) . '</span> In Stock</p>
                    // Old stock logic - commented for future reference
                    // 'stock' => '<p class="table-product-stock mb-0">' . get_post_meta(get_the_ID(), '_stock_' . $suffix, true) . '</p>
                    // 'stock' => '<p class="table-product-stock mb-0">' . (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? 'Typically In Stock </p>
                    //               <p class="small mb-0">Ships in 1 day or less</p>' : 'Not Available from Stock</p>'), // $product->get_price_html(),

                    // New stock logic - show actual numbers based on customer location
                    'stock' => (function() {
                        $main_user_id = get_current_user_id();
                        $company_code = get_user_meta($main_user_id, '_companycode', true);
                        $is_us_customer = ($company_code == "3090");

                        $stock_meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';
                        $live_stock = get_post_meta(get_the_ID(), $stock_meta_key, true);
                        $live_stock = intval($live_stock);

                        // Show 0 instead of negative numbers
                        $display_stock = max(0, $live_stock);

                        return '<p class="table-product-stock mb-0">' . $display_stock . '</p>';
                    })(),
                    'price' => '<p class="table-product-stock mb-0">'.$product->get_price_html().'</p>',
                    'net_price' => '<div class="sap-net-price-loading" data-sku="' . esc_attr($product->get_sku()) . '">Loading...</div>',
                    'brand' => get_post_meta(get_the_ID(), '_brand', true),
                    'product_line' => get_post_meta(get_the_ID(), '_product_line', true),
                    'product_family' => get_post_meta(get_the_ID(), '_product_family', true),
                    'model_size' => get_post_meta(get_the_ID(), '_model_size', true),
                    'ce_approver' => get_post_meta(get_the_ID(), '_ce_approved', true),
                ];
            }
            wp_reset_postdata();
        }
    }

    // $args = [
    //     'post__in' => $filtered_ids,
    //     'post_type' => $post_type,
    //     'posts_per_page' => isset($_POST['length']) ? $_POST['length'] : 10,
    //     'paged' => isset($_POST['start']) ? (intval($_POST['start']) / intval($_POST['length']) + 1) : 1,
    //     's' => $search_value,
    //     'meta_query' => $meta_query,
    //     //'orderby'        => $sort_by === 'default' ? 'date' : $sort_by,
    //     'orderby' => 'post__in'
    // ];
    // // var_dump($args);
    // // exit;

    // // Execute the product query
    // $query = new WP_Query($args);
    // Loop through the products and format the data

    // Prepare response in DataTables format
    $response = [
        "draw" => intval($_POST['draw']),
        "recordsTotal" => empty($filtered_ids) ? 1 : (isset($query) ? $query->found_posts : 0),
        "recordsFiltered" => empty($filtered_ids) ? 1 : (isset($query) ? $query->found_posts : 0),
        "data" => $products,
        // Debug info - remove after testing
        "debug" => [
            "filtered_ids_count" => count($filtered_ids),
            "products_count" => count($products),
            "query_found_posts" => isset($query) ? $query->found_posts : 0,
            "sample_product" => !empty($products) ? array_keys($products[0]) : [],
            "post_data" => $_POST
        ]
    ];

    wp_send_json($response);
}

function get_filtered_product_price_code($product_sku, $filtering_code_ids, $ce_approvers)
{
    global $wpdb;
    $table_shuffix = "";
    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $customer_id = get_user_meta($main_user_id, '_customer', true);
    if ($customer_id !== "") {
        $company_code = get_user_meta($main_user_id, '_companycode', true);
        $country_code = get_user_meta($main_user_id, '_country', true);
        if ($company_code == "3090") {
            $table_shuffix = "";
        } else {
            if ($country_code == "GB") {
                $table_shuffix = "_gbp";
            } else {
                $table_shuffix = "_eur";
            }
        }

        $_SESSION["table_shuffix"] = $table_shuffix;
        $price_list_table = $wpdb->prefix . 'price_list' . $table_shuffix;
        $price_code_list_table = $wpdb->prefix . 'price_code_list' . $table_shuffix;
        $sql = "
			SELECT pcl.price_code_title FROM $price_list_table pl
			JOIN $price_code_list_table pcl
			ON pl.price_code_id=pcl.id
			WHERE pl.product_sku='$product_sku'";
        $res = $wpdb->get_col($wpdb->prepare($sql));
        if (count($filtering_code_ids) > 0) {
            if (in_array($res[0], $filtering_code_ids)) {
                return $res[0];
            } else {
                return false;
            }
        } else {
            return $res[0] ? $res[0] : "No code";
        }
    }
}
function get_total_price_code()
{
    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $get_data = get_product_for_customer_by_id($main_user_id, [], []); // Retrieve all available product IDs for logged user
    return $get_data['allowed_price_codes'];
}
function get_custom_field_values($field_key)
{
    global $wpdb;

    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $get_data = get_product_for_customer_by_id($main_user_id, [], []); // Retrieve all available product IDs for logged user

    $filtered_ids = array_merge($get_data['new_price_products'], $get_data['allowed_products']);

    // Query distinct values from the postmeta table for the specified custom field
    $results = $wpdb->get_col($wpdb->prepare("
        SELECT DISTINCT meta_value
        FROM {$wpdb->postmeta}
        WHERE post_id IN (" . implode(',', array_map('intval', $filtered_ids)) . ")
		AND meta_key = %s
        AND meta_value IS NOT NULL
        ORDER BY meta_value ASC
    ", $field_key));

    return $results ? array_filter($results) : [];
}

add_action('wp_enqueue_scripts', 'enqueue_custom_scripts');
function enqueue_custom_scripts()
{
    $ver = filemtime(get_stylesheet_directory() . '/assets/js/custom-datatables.js');
    // Enqueue the DataTable initialization script
    wp_enqueue_script('custom-datatables', get_stylesheet_directory_uri() . '/assets/js/custom-datatables.js?v=' . $ver, array('jquery'), null, true);

    // Fetch distinct values for each custom field and pass them to JavaScript
    $custom_field_data = [
        'brand' => get_custom_field_values('_brand'),
        'product_line' => get_custom_field_values('_product_line'),
        'product_family' => get_custom_field_values('_product_family'),
        'product_series' => get_custom_field_values('_product_series'),
        'model_size' => get_custom_field_values('_model_size'),
        'usd_price_code' => get_total_price_code(),
        'ce_approved' => ["Yes", "No"],
        // 'ce_approved' => get_custom_field_values('ce_approved'),
    ];

    // Use wp_localize_script to make data accessible to JavaScript
    wp_localize_script('custom-datatables', 'customFilterData', $custom_field_data);

    // Also pass the AJAX URL
    wp_localize_script('custom-datatables', 'ajax_object', array('ajax_url' => admin_url('admin-ajax.php')));
}

// ADD TO CART
add_action('wp_enqueue_scripts', 'enqueue_custom_ajax_script');
function enqueue_custom_ajax_script()
{
    $ver = filemtime(get_stylesheet_directory() . '/assets/js/custom-add-to-cart.js');
    wp_enqueue_script('custom-ajax-add-to-cart', get_stylesheet_directory_uri() . '/assets/js/custom-add-to-cart.js?v=' . $ver, array('jquery'), null, true);
    wp_localize_script('custom-ajax-add-to-cart', 'customAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('custom-ajax-nonce')
    ));
}

add_action('wp_ajax_custom_add_to_cart', 'custom_add_to_cart');
add_action('wp_ajax_nopriv_custom_add_to_cart', 'custom_add_to_cart');
function custom_add_to_cart()
{
    check_ajax_referer('custom-ajax-nonce', 'nonce');

    $product_id = intval($_POST['product_id']);
    $quantity = intval($_POST['quantity']);

    $product = wc_get_product($product_id);
    if (!$product || !$product->is_purchasable()) {
        wp_send_json_error(array('message' => 'Invalid product.'));
    }

    $suffix = get_stock_suffix();
    $count_before_cart = get_post_meta($product_id, '_stock_' . $suffix, true);
    $back_order_status = get_post_meta($product_id, '_backorders_' . $suffix, true);

    // Logging disabled for production
    // my_plugin_custom_log("Count before cart: $count_before_cart, quantity: $quantity, back order status: $back_order_status, \suffix : $suffix, product id: $product_id");

    if ((int)$count_before_cart < (int) $quantity && $back_order_status === 'no') {
        wp_send_json_error(array('message' => 'Sorry, insufficient stock for this region.'));
    } else {
        try{
            $result = WC()->cart->add_to_cart($product_id, $quantity);
            // my_plugin_custom_log("Add result:" . $result);

            if ($result) {
                wc_add_to_cart_message(array($product_id => $quantity), true);
                // if ((int)$count_before_cart < (int) $quantity && $back_order_status === 'yes') {
                //     wp_send_json_success(array('message' => 'Product is available only ' . $count_before_cart . ' and '.(int)$quantity - (int)$count_before_cart.' items are in backorder for your region now!', 'cart_count' => WC()->cart->get_cart_contents_count()));
                // } else {
                //     wp_send_json_success(array('message' => 'Product added to cart successfully!', 'cart_count' => WC()->cart->get_cart_contents_count()));
                // }
                wp_send_json_success(array('message' => 'Product added to cart successfully!', 'cart_count' => WC()->cart->get_cart_contents_count()));
            } else {
                wp_send_json_error(array('message' => 'Failed to add product to cart.'));
            }
        }catch(WCException $e){
            my_plugin_custom_log('Error add cart message , '. $e->getMessage());
        }

    }

    // if ((int)$count_before_cart < (int) $quantity && $back_order_status === 'yes') {
    //     wc_add_notice(__('This product is on backorder for your region.'), 'notice');
    // }

    // if ((int) $count_before_cart == 0) {
    //     wp_send_json_error(array('message' => 'Not available add to cart. Product is in out of stock.'));
    // } else if ((int) $count_before_cart < (int) $quantity) {
    //     wp_send_json_error(array('message' => 'Product is available only ' . $count_before_cart . ' item(s) now.'));
    // } else {
    //     $result = WC()->cart->add_to_cart($product_id, $quantity);

    //     if ($result) {
    //         wc_add_to_cart_message(array($product_id => $quantity), true);
    //         wp_send_json_success(array('message' => 'Product added to cart successfully!', 'cart_count' => WC()->cart->get_cart_contents_count()));
    //     } else {
    //         wp_send_json_error(array('message' => 'Failed to add product to cart.'));
    //     }
    // }

    wp_die();
}

add_action('wp_ajax_custom_recreate_filters', 'custom_recreate_filters');
add_action('wp_ajax_nopriv_custom_recreate_filters', 'custom_recreate_filters');
function fetch_filtered_options()
{
    global $wpdb;

    // Retrieve selected values from AJAX request
    $current_user = wp_get_current_user();
    $get_data = $_SESSION['total_get_data']; // Retrieve all available product IDs for logged user

    $filtered_ids = array_merge($get_data['new_price_products'], $get_data['allowed_products']);
    // Retrieve selected values from AJAX request
    $selected_values = isset($_POST['selectedValues']) ? $_POST['selectedValues'] : [];
    $selected_price_code = $selected_values['usd_price_code'] > 0 ? $selected_values['usd_price_code'] : [];
    // Create a unique cache key based on selected values
    $cache_key = 'filtered_options_' . md5(json_encode($selected_values));
    $filtered_options = get_transient($cache_key);

    if ($filtered_options === false) {
        // Data is not in cache, build it
        $fields = [
            'brand' => '_brand',
            'product_line' => '_product_line',
            'product_family' => '_product_family',
            'product_series' => '_product_series',
            'model_size' => '_model_size',
            // 'usd_price_code' => '_usd_price_code',
            'ce_approved' => '_ce_approved'
        ];

        $filtered_options = [];

        foreach ($fields as $field_id => $meta_key) {
            if (!empty($selected_values)) {
                $meta_query = ['relation' => 'AND'];

                foreach ($selected_values as $key => $values) {
                    if (!empty($values)) {
                        $meta_query[] = [
                            'key' => $fields[$key],
                            'value' => $values,
                            'compare' => 'IN'
                        ];
                    }
                }

                $args = [
                    'post__id' => $filtered_ids,
                    'post_type' => 'product',
                    'posts_per_page' => -1,
                    'meta_query' => $meta_query,
                    'fields' => 'ids'
                ];

                $query = new WP_Query($args);
                $product_ids = $query->posts;
                if (!empty($product_ids)) {
                    // Fetch unique values for the current custom field based on filtered product IDs
                    $results = $wpdb->get_col($wpdb->prepare("
                        SELECT DISTINCT meta_value
                        FROM {$wpdb->postmeta}
                        WHERE meta_key = %s
                        AND post_id IN (" . implode(',', array_map('intval', $product_ids)) . ")
                    ", $meta_key));

                    $filtered_options[$field_id] = $results ? array_filter($results) : [];
                }
            } else {
                // If no values are selected, get all distinct values for the field
                $results = $wpdb->get_col($wpdb->prepare("
                    SELECT DISTINCT meta_value
                    FROM {$wpdb->postmeta}
                    WHERE meta_key = %s
                    AND meta_value IS NOT NULL
                    ORDER BY meta_value ASC
                ", $meta_key));

                $filtered_options[$field_id] = $results ? array_filter($results) : [];
            }
        }

        // This is filtering for price_code
        $table_shuffix = $_SESSION["table_shuffix"];
        $price_list_table = $wpdb->prefix . "price_list" . $table_shuffix;
        $price_code_table = $wpdb->prefix . "price_code_list" . $table_shuffix;

        if (!empty($selected_values)) {
            foreach ($selected_values as $key => $values) {
                if (!empty($values) && $key !== 'usd_price_code') {
                    $meta_query[] = [
                        'key' => $fields[$key],
                        'value' => $values,
                        'compare' => 'IN'
                    ];
                }
            }

            $args = [
                'post__id' => $filtered_ids,
                'post_type' => 'product',
                'posts_per_page' => -1,
                'meta_query' => $meta_query,
                'fields' => 'ids'
            ];

            $query = new WP_Query($args);
            $product_ids = $query->posts;

            if (!empty($product_ids)) {
                $product_ids_str = implode(',', array_map('intval', $product_ids));
                $query = $wpdb->prepare("
                    SELECT DISTINCT pcl.price_code_title
                    FROM {$wpdb->postmeta} pm
                    INNER JOIN {$price_list_table} pl ON pm.meta_value = pl.product_sku
                    INNER JOIN {$price_code_table} pcl ON pl.price_code_id = pcl.id
                    WHERE pm.meta_key = '_sku'
                    AND pm.post_id IN ($product_ids_str)
                ", $product_ids_str);

                $results = $wpdb->get_col($query);
                $filtered_options['usd_price_code'] = $results ? array_filter($results) : [];
            }
        } else {
            $filtered_options['usd_price_code'] = $get_data['allowed_price_codes'];
        }
        // Store the result in transient cache for 15 minutes
        set_transient($cache_key, $filtered_options, 15 * MINUTE_IN_SECONDS);
    }
    wp_send_json($filtered_options);
}
add_action('wp_ajax_fetch_filtered_options', 'fetch_filtered_options');
add_action('wp_ajax_nopriv_fetch_filtered_options', 'fetch_filtered_options');

function get_logged_user_admin_id()
{
    if (is_user_logged_in()) {
        $cur_user = wp_get_current_user();
        $user_roles = $cur_user->roles;

        if (!in_array('b2b_administrator', $user_roles) && !in_array('administrator', $user_roles)) {
            return get_user_meta($cur_user->ID, '_parent_admin_id', true);
        } else {
            return $cur_user->ID;
        }
    }
}

// customize single product hooks
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_title', 5);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_price', 10);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40);

add_action('woocommerce_single_product_summary', 'woocommerce_template_single_meta', 5);
// add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_title', 40 );
// add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_price', 10 );
// add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20 );
add_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 10);

// Reorder and rename items in My Account sidebar.
add_filter('woocommerce_account_menu_items', 'custom_my_account_menu_items', 99);
function custom_my_account_menu_items($items)
{

    // Rename items
    $items['dashboard'] = __('Dashboard', 'yolo-motor');
    $items['orders'] = __('Orders', 'yolo-motor');
    $items['downloads'] = __('Downloads', 'yolo-motor');
    $items['edit-address'] = __('Addresses', 'yolo-motor');
    $items['edit-account'] = __('Account Details', 'yolo-motor');
    // $items['company-accounts'] = __('Company Accounts', 'yolo-motor');
    $items['shopping-lists'] = __('Shopping Lists', 'yolo-motor');
    $items['customer-logout'] = __('Logout', 'yolo-motor');

    $ordered_items = array();
    $current_user = wp_get_current_user();

    if (in_array('b2b_sales', $current_user->roles)) {
        $ordered_items = array(
            'dashboard' => $items['dashboard'],
            'shopping-lists' => $items['shopping-lists'],
            // 'company-accounts' => $items['company-accounts'],
            'edit-address' => $items['edit-address'],
            'edit-account' => $items['edit-account'],
            'customer-logout' => $items['customer-logout'],
        );
    } else {
        $ordered_items = array(
            'dashboard' => $items['dashboard'],
            'orders' => $items['orders'],
            'shopping-lists' => $items['shopping-lists'],
            // 'company-accounts' => $items['company-accounts'],
            'edit-address' => $items['edit-address'],
            'edit-account' => $items['edit-account'],
            'customer-logout' => $items['customer-logout'],
        );
    }
    return $ordered_items;
}

add_filter('woocommerce_checkout_fields', 'change_billing_details_title', 999);
function change_billing_details_title($fields)
{
    add_filter('woocommerce_checkout_fields', function ($checkout_fields) {
        $checkout_fields['billing']['billing_title'] = 'Contact Information';
        return $checkout_fields;
    });

    return $fields;
}

add_filter('woocommerce_checkout_fields', 'customize_billing_fields');
function customize_billing_fields($fields)
{
    // Only keep email in the billing fields (phone gets removed by another function anyway)
    $allowed_fields = ['billing_email'];

    foreach ($fields['billing'] as $field_key => $field) {
        if (!in_array($field_key, $allowed_fields)) {
            unset($fields['billing'][$field_key]);
        }
    }

    return $fields;
}
add_filter('woocommerce_cart_needs_shipping_address', '__return_true');
add_filter('woocommerce_checkout_fields', 'disable_ship_to_different_address_option');
function disable_ship_to_different_address_option($fields)
{
    // Remove the "Ship to a different address?" checkbox
    add_filter('woocommerce_ship_to_different_address_checked', '__return_true');

    return $fields;
}

add_filter('woocommerce_checkout_fields', 'customize_shipping_fields');
function customize_shipping_fields($fields)
{
    // Set the company field as required to avoid "(optional)"
    if (isset($fields['shipping']['shipping_company'])) {
        $fields['shipping']['shipping_company']['required'] = true;
    }

    return $fields;
}

add_filter('woocommerce_checkout_fields', 'reorder_shipping_fields_priority');
function reorder_shipping_fields_priority($fields)
{
    // Set the priorities for shipping fields to reorder them
    if (isset($fields['shipping'])) {
        $fields['shipping']['shipping_company']['priority'] = 10;  // Company first
        $fields['shipping']['shipping_first_name']['priority'] = 20;  // First name after company
        $fields['shipping']['shipping_last_name']['priority'] = 30;  // Last name after first name
        $fields['shipping']['shipping_address_1']['priority'] = 40;  // Address line 1
        $fields['shipping']['shipping_address_2']['priority'] = 50;  // Address line 2
        $fields['shipping']['shipping_city']['priority'] = 60;  // City
        $fields['shipping']['shipping_postcode']['priority'] = 70;  // Postcode
        $fields['shipping']['shipping_state']['priority'] = 80;  // State/County
        $fields['shipping']['shipping_country']['priority'] = 90;  // Country
    }

    return $fields;
}

add_filter('woocommerce_cart_shipping_method_full_label', 'customize_shipping_label', 10, 2);
function customize_shipping_label($label, $method)
{
    // Replace the shipping label with custom text
    $label = 'Later';
    return $label;
}

add_filter('woocommerce_shipping_package_name', 'customize_shipping_package_name', 10, 3);
function customize_shipping_package_name($package_name, $i, $package)
{
    // Replace the default "Shipping" text with "Delivery"
    return 'Delivery';
}

add_action('wp_footer', 'replace_total_label_js');
function replace_total_label_js()
{
    if (is_checkout()) {
        ?>
        <script>
            function replaceTotalLabel() {
                // Replace "Total" label with "Estimated Subtotal"
                var totalLabels = document.querySelectorAll('.woocommerce-checkout-review-order-table th');
                totalLabels.forEach(function (label) {
                    if (label.innerText.trim() === 'Total') {
                        label.innerText = 'Estimated Subtotal';
                    }
                });

                // Also replace in mini-cart totals if needed
                var totalSummaryLabels = document.querySelectorAll('.cart-subtotal th, .order-total th');
                totalSummaryLabels.forEach(function (label) {
                    if (label.innerText.trim() === 'Total') {
                        label.innerText = 'Estimated Subtotal';
                    }
                });

                document.getElementById('order_review_heading').innerText = 'Order Summary';
                document.querySelector('.woocommerce-privacy-policy-text').innerText = 'Applicable net pricing and shipping with be calculated with your company rep when your order is received on our system.';
            }

            document.addEventListener('DOMContentLoaded', function () {
                replaceTotalLabel(); // Run on page load
            });

            jQuery(document.body).on('updated_checkout', function () {
                replaceTotalLabel(); // Run on checkout update
            });
        </script>
        <?php
    }
}

add_action('woocommerce_review_order_before_submit', 'add_terms_checkbox', 9);
function add_terms_checkbox()
{
    ?>
    <p class="form-row terms">
        <span class="custom-check woocommerce-form__label woocommerce-form__label-for-checkbox checkbox p-0">
            <input type="checkbox" class="woocommerce-form__input-checkbox" name="terms_agreement" id="terms_agreement" />
            <label for="terms_agreement" class="ml-1">I agree to the <a
                    href="https://hydraulictechnologies.com/terms-and-conditions" target="_blank">Terms
                    and Conditions agreement</a>&nbsp;<span class="required">*</span></label>
        </span>
    </p>
    <?php
}

add_action('woocommerce_checkout_process', 'validate_terms_checkbox');
function validate_terms_checkbox()
{
    if (!isset($_POST['terms_agreement'])) {
        wc_add_notice(__('Please read and accept the Terms and Conditions agreement to proceed.'), 'error');
    }
}

add_filter('woocommerce_checkout_terms_and_conditions_message', 'replace_privacy_text');
function replace_privacy_text($text)
{
    // Replace the existing privacy text with custom text
    $custom_text = 'Applicable net pricing and shipping will be calculated with your company rep when your order is received on our system.';
    return $custom_text;
}

add_filter('woocommerce_locate_template', 'force_custom_review_order_template', 10, 3);
function force_custom_review_order_template($template, $template_name, $template_path)
{
    if ($template_name == 'checkout/review-order.php') {
        $custom_template = get_stylesheet_directory() . '/woocommerce/checkout/review-order.php';
        if (file_exists($custom_template)) {
            return $custom_template;
        }
    }
    return $template;
}

add_action('wp_footer', 'customize_cart_page_header_js');
function customize_cart_page_header_js()
{
    if (is_cart()) {
        $cart_count = 0;
        foreach (WC()->cart->get_cart() as $cart_item) {
            $cart_count++; // Count each unique item in the cart
        }
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // Select the <h1> element for the cart title
                var cartTitleElement = document.querySelector('h1.page-title');

                if (cartTitleElement) {
                    // Get the number of items in the cart
                    var cartCount = <?php echo $cart_count ?>;

                    // Update the title text
                    if (cartCount > 0) {
                        cartTitleElement.innerHTML = 'Cart <span class="green">' + cartCount + ' items</span>';

                        // Create the Empty Cart link
                        var emptyCartLink = document.createElement('a');
                        emptyCartLink.href = "javascript:;";
                        emptyCartLink.className = 'empty-cart-link';
                        emptyCartLink.id = 'empty-cart-button';
                        emptyCartLink.textContent = 'Empty Cart';
                        emptyCartLink.style.marginLeft = '15px'; // Add some spacing

                        // Append the link next to the cart title
                        cartTitleElement.appendChild(emptyCartLink);
                    } else {
                        cartTitleElement.textContent = 'Cart';
                    }
                }
            });
        </script>
        <?php
    }
}

add_filter('gettext', 'custom_thank_you_text', 20, 3);
function custom_thank_you_text($translated_text, $text, $domain)
{
    if ('woocommerce' === $domain && 'Thank You For Your Order.' === $text) {
        $translated_text = 'Thank you for your oder submission, Your order ID is ********. Your account representative will follow up for order finalization within 2 business days.';
    }
    return $translated_text;
}

// add_filter('woocommerce_order_button_text', 'custom_checkout_button_label');
// function custom_checkout_button_label($button_text)
// {
//     return 'Go To Last Step';
// }

add_filter('gettext', 'custom_no_address_text', 20, 3);
function custom_no_address_text($translated_text, $text, $domain)
{
    if ('woocommerce' === $domain && 'You have not set up this type of address yet.' === $text) {
        $translated_text = 'You do not have the billing address set yet, please contact us.';
    }
    return $translated_text;
}

add_action('wp_ajax_nopriv_update_cart_count', 'update_cart_count');
add_action('wp_ajax_update_cart_count', 'update_cart_count');
function update_cart_count()
{
    $cart_count = 0;
    foreach (WC()->cart->get_cart() as $cart_item) {
        $cart_count++; // Count each unique item in the cart
    }
    echo $cart_count;

    wp_die(); // Always use wp_die() after an AJAX function to end execution.
}

include 'functions-kekacbre.php';

// ========================================
// CUSTOM ADDRESS MANAGEMENT FUNCTIONS
// ========================================

/**
 * Get addresses from wp_sap_shipto_addresses table for a user
 *
 * @param int $user_id WordPress user ID
 * @return array|false Array of addresses or false if none found
 */
function get_custom_user_addresses($user_id) {
    global $wpdb;

    if (!$user_id) {
        return false;
    }

    // Get the main admin user ID (for B2B accounts)
    $main_user_id = get_main_b2b_admin_id($user_id);

    // Get customer ID from user meta
    $customer_id = get_user_meta($main_user_id, '_customer', true);

    if (!$customer_id) {
        return false;
    }

/**
 * Log SAP checkout process to dedicated log file
 *
 * @param string $message Log message
 * @param array $data Additional data to log
 */
function hytec_log_sap_checkout($message, $data = array()) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/checkout-sap.log';

    // Create logs directory if it doesn't exist
    if (!file_exists($log_dir)) {
        wp_mkdir_p($log_dir);
    }

    $timestamp = current_time('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}";

    if (!empty($data)) {
        $log_entry .= "\nData: " . json_encode($data, JSON_PRETTY_PRINT);
    }

    $log_entry .= "\n" . str_repeat('-', 80) . "\n";

    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

/**
 * Create SAP Sales Order via API call
 *
 * @param int $order_id WooCommerce order ID
 * @return array|WP_Error SAP API response or error
 */
function hytec_create_sap_sales_order($order_id) {
    hytec_log_sap_checkout("Starting SAP order creation for order ID: {$order_id}");

    $order = wc_get_order($order_id);
    if (!$order) {
        hytec_log_sap_checkout("ERROR: Order not found", array('order_id' => $order_id));
        return new WP_Error('invalid_order', 'Order not found');
    }

    // Build the SAP order payload
    hytec_log_sap_checkout("Building SAP order payload for order {$order_id}");
    $payload = hytec_build_sap_order_payload($order);
    if (is_wp_error($payload)) {
        hytec_log_sap_checkout("ERROR: Failed to build SAP payload", array(
            'order_id' => $order_id,
            'error' => $payload->get_error_message()
        ));
        return $payload;
    }

    // Log the request payload
    hytec_log_sap_checkout("SAP Request Payload for order {$order_id}", $payload);

    // Store the request payload for debugging
    update_post_meta($order_id, '_sap_order_request', wp_json_encode($payload, JSON_PRETTY_PRINT));

    // Get SAP API configuration
    $url = 'https://hytec-dev-8ouoom67.it-cpi034-rt.cfapps.us10-002.hana.ondemand.com/http/WC/SalesOrder';
    $auth = hytec_sap_api_get_auth_header();

    if (empty($auth)) {
        hytec_log_sap_checkout("ERROR: SAP authentication token not available", array('order_id' => $order_id));
        return new WP_Error('sap_auth', 'SAP authentication token not available');
    }

    // Make the API call
    $args = array(
        'method'      => 'POST',
        'timeout'     => 30,
        'headers'     => array(
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer [REDACTED]', // Don't log the actual token
        ),
        'body'        => wp_json_encode($payload),
        'data_format' => 'body',
    );

    hytec_log_sap_checkout("Making SAP API call for order {$order_id}", array(
        'url' => $url,
        'method' => 'POST',
        'timeout' => 30,
        'has_auth' => !empty($auth)
    ));

    $response = wp_remote_post($url, array(
        'method'      => 'POST',
        'timeout'     => 30,
        'headers'     => array(
            'Content-Type'  => 'application/json',
            'Authorization' => $auth,
        ),
        'body'        => wp_json_encode($payload),
        'data_format' => 'body',
    ));

    if (is_wp_error($response)) {
        hytec_log_sap_checkout("ERROR: SAP API call failed for order {$order_id}", array(
            'error_code' => $response->get_error_code(),
            'error_message' => $response->get_error_message()
        ));
        return $response;
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    // Log the response
    hytec_log_sap_checkout("SAP API Response for order {$order_id}", array(
        'response_code' => $response_code,
        'response_body' => $response_body
    ));

    // Store the response in order meta
    update_post_meta($order_id, '_sap_order_response', $response_body);
    update_post_meta($order_id, '_sap_order_response_code', $response_code);

    if ($response_code >= 200 && $response_code < 300) {
        // HTTP success, but check SAP response for actual success/failure
        $decoded_response = json_decode($response_body, true);

        hytec_log_sap_checkout("Processing SAP response for order {$order_id}", array(
            'decoded_response' => $decoded_response
        ));

        // Check if SAP returned any errors in the response
        $has_errors = false;
        $error_messages = array();
        if (is_array($decoded_response) && isset($decoded_response['return']) && is_array($decoded_response['return'])) {
            foreach ($decoded_response['return'] as $return_item) {
                if (isset($return_item['TYPE']) && $return_item['TYPE'] === 'E') {
                    $has_errors = true;
                    $error_messages[] = $return_item['MESSAGE'] ?? 'Unknown error';
                }
            }
        }

        if ($has_errors) {
            // SAP returned errors, treat as failure
            hytec_log_sap_checkout("SAP returned business errors for order {$order_id}", array(
                'error_messages' => $error_messages,
                'full_response' => $decoded_response
            ));
            update_post_meta($order_id, '_sap_order_created', 'no');
            update_post_meta($order_id, '_sap_order_error', 'SAP returned errors: ' . implode(', ', $error_messages));
            return new WP_Error('sap_business_error', 'SAP returned business errors', array('body' => $response_body, 'decoded' => $decoded_response));
        } else {
            // True success
            hytec_log_sap_checkout("SAP order creation successful for order {$order_id}", array(
                'sap_order_id' => $decoded_response['order_id'] ?? 'N/A',
                'sap_sales_order_number' => $decoded_response['sap_sales_order_number'] ?? 'N/A'
            ));
            update_post_meta($order_id, '_sap_order_created', 'yes');
            return $decoded_response;
        }
    } else {
        // HTTP Error
        hytec_log_sap_checkout("SAP API returned HTTP error for order {$order_id}", array(
            'response_code' => $response_code,
            'response_body' => $response_body
        ));
        return new WP_Error('sap_api_error', 'SAP API returned error: ' . $response_code, array('body' => $response_body));
    }
}

/**
 * Build SAP order payload from WooCommerce order
 *
 * @param WC_Order $order WooCommerce order object
 * @return array|WP_Error SAP order payload or error
 */
function hytec_build_sap_order_payload($order) {
    $order_id = $order->get_id();
    $user_id = $order->get_user_id();

    hytec_log_sap_checkout("Building SAP payload for order {$order_id}", array(
        'user_id' => $user_id
    ));

    if (!$user_id) {
        hytec_log_sap_checkout("ERROR: Order {$order_id} has no associated user");
        return new WP_Error('no_user', 'Order has no associated user');
    }

    // Get the main admin user ID (for B2B accounts)
    $main_user_id = get_main_b2b_admin_id($user_id);
    hytec_log_sap_checkout("User mapping for order {$order_id}", array(
        'original_user_id' => $user_id,
        'main_user_id' => $main_user_id
    ));

    // Get customer data from soldto table
    $customer_data = hytec_get_sap_customer_for_user($main_user_id);
    if (!$customer_data) {
        hytec_log_sap_checkout("ERROR: No SAP customer data found for order {$order_id}", array(
            'main_user_id' => $main_user_id
        ));
        return new WP_Error('no_customer_data', 'No SAP customer data found for user');
    }

    hytec_log_sap_checkout("Customer data retrieved for order {$order_id}", array(
        'customer_id' => $customer_data->customer_id,
        'company_code' => $customer_data->company_code ?? 'N/A',
        'Z7_Partner_no' => $customer_data->Z7_Partner_no ?? 'N/A'
    ));

    // Get order meta fields
    $purchase_order_number = get_post_meta($order->get_id(), '_purchase_order_number', true);
    $customer_requested_ship_date = get_post_meta($order->get_id(), '_customer_requested_ship_date', true);
    $shipping_method_preference = get_post_meta($order->get_id(), '_shipping_method_preference', true);
    $shipping_account_number = get_post_meta($order->get_id(), '_shipping_account_number', true);
    $special_requests = get_post_meta($order->get_id(), '_special_requests', true);

    // Map shipping method preference to SAP shipping conditions and account
    $shipping_mapping = hytec_map_shipping_method_to_sap($shipping_method_preference);
    $sap_shipping_conditions = $shipping_mapping['conditions'];
    $sap_shipping_account = $shipping_mapping['account'];

    // Get selected shipping address ID from checkout - try multiple possible meta keys
    $selected_address_id = get_post_meta($order->get_id(), '_wcmca_shipping_selected_address_id', true);

    // Try alternative meta keys if the first one is empty
    if (empty($selected_address_id)) {
        $selected_address_id = get_post_meta($order->get_id(), '_custom_shipping_address_id', true);
    }

    // Try to get from WCMCA plugin's standard meta
    if (empty($selected_address_id)) {
        $selected_address_id = get_post_meta($order->get_id(), '_wcmca_shipping_address', true);
    }

    // Try to get from shipping address data
    if (empty($selected_address_id)) {
        $shipping_address_data = get_post_meta($order->get_id(), '_custom_shipping_address_data', true);
        if (is_array($shipping_address_data) && !empty($shipping_address_data['address_id'])) {
            $selected_address_id = $shipping_address_data['address_id'];
        }
    }

    // Final fallback - try to get the first available address for this customer
    if (empty($selected_address_id)) {
        $customer_addresses = get_custom_user_addresses($main_user_id);
        if (is_array($customer_addresses) && !empty($customer_addresses)) {
            // Get the first shipping address
            foreach ($customer_addresses as $address) {
                if (isset($address['type']) && $address['type'] === 'shipping' && !empty($address['address_id'])) {
                    $selected_address_id = $address['address_id'];
                    break;
                }
            }
        }
    }

    // Ultimate fallback
    if (empty($selected_address_id)) {
        $selected_address_id = 'YSHIP';
    }

    // Format customer ID with leading zeros (10 digits)
    $formatted_customer_id = hytec_format_customer_id($customer_data->customer_id);

    // Format address ID with leading zeros if numeric
    $formatted_address_id = hytec_format_customer_id($selected_address_id);

    // Debug logging
    error_log('SAP Order Payload Debug:');
    error_log('- Raw Customer ID: ' . $customer_data->customer_id);
    error_log('- Formatted Customer ID: ' . $formatted_customer_id);
    error_log('- Raw Address ID: ' . $selected_address_id);
    error_log('- Formatted Address ID: ' . $formatted_address_id);

    // Debug address ID retrieval attempts
    error_log('- Address ID Sources Checked:');
    error_log('  * _wcmca_shipping_selected_address_id: ' . get_post_meta($order->get_id(), '_wcmca_shipping_selected_address_id', true));
    error_log('  * _custom_shipping_address_id: ' . get_post_meta($order->get_id(), '_custom_shipping_address_id', true));
    error_log('  * _wcmca_shipping_address: ' . get_post_meta($order->get_id(), '_wcmca_shipping_address', true));

    // Debug shipping address data
    error_log('- Shipping Address Data:');
    error_log('  * shipping_state: ' . $order->get_shipping_state());
    error_log('  * shipping_country: ' . $order->get_shipping_country());
    error_log('  * shipping_city: ' . $order->get_shipping_city());
    error_log('  * shipping_postcode: ' . $order->get_shipping_postcode());

    // Debug shipping method mapping
    error_log('- Shipping Method Mapping:');
    error_log('  * Raw shipping_method_preference: ' . $shipping_method_preference);
    error_log('  * Mapped SAP shipping_conditions: ' . $sap_shipping_conditions);
    error_log('  * Mapped SAP shipping_account: ' . $sap_shipping_account);
    error_log('  * Full mapping result: ' . json_encode($shipping_mapping));

    if (!empty($customer_data->Z7_Partner_no)) {
        error_log('- Raw Z7_Partner_no: ' . $customer_data->Z7_Partner_no);
        error_log('- Formatted Z7_Partner_no: ' . hytec_format_customer_id($customer_data->Z7_Partner_no));
    }

    // Build billing information
    $billing = array(
        'billing_id' => $formatted_customer_id,
        'first_name' => $order->get_billing_first_name(),
        'last_name' => $order->get_billing_last_name(),
        'company_code' => $customer_data->company_code ?? '',
        'company_name' => $customer_data->company ?? $order->get_billing_company(),
        'address_1' => $order->get_billing_address_1(),
        'address_2' => $order->get_billing_address_2(),
        'city' => $order->get_billing_city(),
        'state' => $order->get_billing_state(),
        'postcode' => $order->get_billing_postcode(),
        'country' => $order->get_billing_country(),
        'email' => $order->get_billing_email(),
        'phone' => $order->get_billing_phone(),
    );

    // Add Z7_Partner_no if it exists (also format with leading zeros if numeric)
    if (!empty($customer_data->Z7_Partner_no)) {
        $billing['Z7_Partner_no'] = hytec_format_customer_id($customer_data->Z7_Partner_no);
    }

    // Build shipping information
    $shipping = array(
        'shipping_id' => $formatted_address_id,
    );

    // Build line items
    $line_items = array();
    foreach ($order->get_items() as $item) {
        $product = $item->get_product();
        if ($product) {
            $line_items[] = array(
                'product_id' => hytec_format_material_id($product->get_sku()),
                'quantity' => floatval($item->get_quantity()),
            );
        }
    }

    // Build the main order payload
    $order_data = array(
        'id' => $purchase_order_number . '-' . $order->get_id(),
        'date_created' => current_time('c'), // ISO 8601 format
        'status' => 'completed',
        'purchase_order' => $purchase_order_number,
        'Cust_Req_Ship_Date' => $customer_requested_ship_date,
        'freight_terms' => $shipping_account_number,
        'shipping_conditions' => $sap_shipping_conditions,
        'shipping_account' => $sap_shipping_account, // ZSO ILI ZOR
        'billing' => $billing,
        'shipping' => $shipping,
        'line_items' => $line_items,
    );

    // Add delivery_block if special requests exist
    if (!empty($special_requests)) {
        $order_data['delivery_block'] = 'ZH';
    }

    return array('order' => $order_data);
}

/**
 * Get complete shipping method mapping data
 *
 * @return array Complete mapping of shipping methods with conditions and accounts
 */
function hytec_get_shipping_method_mapping() {
    return array(
        '0'  => array('conditions' => '0',  'account' => 'ZOR'), // See Shipping Instr.
        '1'  => array('conditions' => '1',  'account' => 'ZOR'), // Standard - Generic
        '02' => array('conditions' => '02', 'account' => 'ZSO'), // Rush Orders
        '03' => array('conditions' => '03', 'account' => 'ZSO'), // FDX Priority Overnight
        '04' => array('conditions' => '04', 'account' => 'ZSO'), // FDX First Overnight
        '05' => array('conditions' => '05', 'account' => 'ZSO'), // FDX Second Day
        '06' => array('conditions' => '06', 'account' => 'ZSO'), // FDX Std Overnight
        '7'  => array('conditions' => '7',  'account' => 'ZOR'), // FDX International Ec
        '8'  => array('conditions' => '8',  'account' => 'ZOR'), // FDX Intern Ecn Frght
        '9'  => array('conditions' => '9',  'account' => 'ZOR'), // FDX Intern First
        '0A' => array('conditions' => '0A', 'account' => 'ZOR'), // DHL
        '0B' => array('conditions' => '0B', 'account' => 'ZOR'), // UPS
        '0C' => array('conditions' => '0C', 'account' => 'ZOR'), // Pickup
        '10' => array('conditions' => '10', 'account' => 'ZOR'), // FDX Intern Priority
        '11' => array('conditions' => '11', 'account' => 'ZOR'), // FDX Int Priority Frt
        '12' => array('conditions' => '12', 'account' => 'ZOR'), // FDX Ground
        '13' => array('conditions' => '13', 'account' => 'ZOR'), // FDX 1st Day Freight
        '14' => array('conditions' => '14', 'account' => 'ZOR'), // FDX 2nd Day Freight
        '15' => array('conditions' => '15', 'account' => 'ZOR'), // FDX 3rd Day Freight
        '16' => array('conditions' => '16', 'account' => 'ZOR'), // FDX Express Saver
        '17' => array('conditions' => '17', 'account' => 'ZOR'), // FDX Residential
        '18' => array('conditions' => '18', 'account' => 'ZSO'), // UPS Next Day Air
        '19' => array('conditions' => '19', 'account' => 'ZSO'), // UPS NDA Saver
        '20' => array('conditions' => '20', 'account' => 'ZSO'), // UPS ND Early AM
        '21' => array('conditions' => '21', 'account' => 'ZSO'), // UPS Second Day Air
        '22' => array('conditions' => '22', 'account' => 'ZSO'), // UPS 2nd Day Air A.M.
        '23' => array('conditions' => '23', 'account' => 'ZOR'), // UPS SonicAir Best Fl
        '24' => array('conditions' => '24', 'account' => 'ZOR'), // UPS Three Day Select
        '25' => array('conditions' => '25', 'account' => 'ZOR'), // UPS Ground
        '26' => array('conditions' => '26', 'account' => 'ZOR'), // UPS CA Expedited
        '27' => array('conditions' => '27', 'account' => 'ZOR'), // UPS CA Express
        '28' => array('conditions' => '28', 'account' => 'ZOR'), // UPS CA Express Plus
        '29' => array('conditions' => '29', 'account' => 'ZOR'), // UPS Standard for CA
        '30' => array('conditions' => '30', 'account' => 'ZOR'), // UPS Intl Expedited
        '31' => array('conditions' => '31', 'account' => 'ZOR'), // UPS Intl Express
        '32' => array('conditions' => '32', 'account' => 'ZSO'), // UPS ND Saturday Air
        '33' => array('conditions' => '33', 'account' => 'ZSO'), // DHL ND 10:30 AM
        '34' => array('conditions' => '34', 'account' => 'ZSO'), // DHL ND 12:00 pm
        '35' => array('conditions' => '35', 'account' => 'ZSO'), // DHL Next Day 3:00 PM
        '36' => array('conditions' => '36', 'account' => 'ZSO'), // DHL 2nd Day
        '37' => array('conditions' => '37', 'account' => 'ZSO'), // DHL ND 10:30 Letter
        '38' => array('conditions' => '38', 'account' => 'ZSO'), // DHL ND 12:00 Letter
        '39' => array('conditions' => '39', 'account' => 'ZSO'), // DHL ND 3:00 Letter
        '40' => array('conditions' => '40', 'account' => 'ZSO'), // DHL 2nd Day Letter
        '41' => array('conditions' => '41', 'account' => 'ZOR'), // DHL Ground
        '42' => array('conditions' => '42', 'account' => 'ZOR'), // DHL Medml@Home Def
        '43' => array('conditions' => '43', 'account' => 'ZOR'), // DHL Medml@Home Std
        '44' => array('conditions' => '44', 'account' => 'ZOR'), // USPS BPM@Home Def
        '45' => array('conditions' => '45', 'account' => 'ZOR'), // USPS BPM @Home Std
        '46' => array('conditions' => '46', 'account' => 'ZOR'), // USPS@Home Def
        '47' => array('conditions' => '47', 'account' => 'ZOR'), // USPS@Home Std
        '48' => array('conditions' => '48', 'account' => 'ZOR'), // DHL Global Mail Prio
        '49' => array('conditions' => '49', 'account' => 'ZOR'), // DHL Global Mail Std
        '50' => array('conditions' => '50', 'account' => 'ZOR'), // DHL Int Document
        '51' => array('conditions' => '51', 'account' => 'ZOR'), // DHL Int Document Ltr
        '52' => array('conditions' => '52', 'account' => 'ZOR'), // DHL Wrld Wd Priorty
        '53' => array('conditions' => '53', 'account' => 'ZOR'), // ABF Freight Sys Std
        '55' => array('conditions' => '55', 'account' => 'ZOR'), // FedEx Freight Std
        '56' => array('conditions' => '56', 'account' => 'ZOR'), // DHL Wrld Wd Express
        '57' => array('conditions' => '57', 'account' => 'ZOR'), // New Eng Motor Fr Std
        '59' => array('conditions' => '59', 'account' => 'ZOR'), // Old Dominion Std
        '61' => array('conditions' => '61', 'account' => 'ZOR'), // CCX Standard
        '62' => array('conditions' => '62', 'account' => 'ZOR'), // CCX Guaranteed
        '63' => array('conditions' => '63', 'account' => 'ZOR'), // Central Trpt Int Std
        '65' => array('conditions' => '65', 'account' => 'ZOR'), // UPS Supply Chain AM
        '66' => array('conditions' => '66', 'account' => 'ZOR'), // UPS Supply Chain 2DY
        '67' => array('conditions' => '67', 'account' => 'ZOR'), // UPS Supply Chain ECO
        '68' => array('conditions' => '68', 'account' => 'ZOR'), // UPS Supply Chain STD
        '69' => array('conditions' => '69', 'account' => 'ZOR'), // Customer Pickup
        '70' => array('conditions' => '70', 'account' => 'ZOR'), // Generic Carrier
        '71' => array('conditions' => '71', 'account' => 'ZOR'), // Cavalier Transport
        '72' => array('conditions' => '72', 'account' => 'ZOR'), // UPS Supply Chain
        '73' => array('conditions' => '73', 'account' => 'ZOR'), // BAX Global
        '74' => array('conditions' => '74', 'account' => 'ZOR'), // Air Freight
        '75' => array('conditions' => '75', 'account' => 'ZOR'), // Sea Freight
        '76' => array('conditions' => '76', 'account' => 'ZOR'), // Ground
        '77' => array('conditions' => '77', 'account' => 'ZSO'), // Next Day Air
        '78' => array('conditions' => '78', 'account' => 'ZSO'), // 2nd Day Air
        '79' => array('conditions' => '79', 'account' => 'ZSO'), // 3rd Day Air
        '80' => array('conditions' => '80', 'account' => 'ZOR'), // International Expres
        '81' => array('conditions' => '81', 'account' => 'ZOR'), // International Priori
        '82' => array('conditions' => '82', 'account' => 'ZOR'), // Ground Express
        '83' => array('conditions' => '83', 'account' => 'ZOR'), // Counter To Counter
        '84' => array('conditions' => '84', 'account' => 'ZOR'), // Economy
        '85' => array('conditions' => '85', 'account' => 'ZOR'), // Saturday Delivery
        '86' => array('conditions' => '86', 'account' => 'ZOR'), // Expedited Service
        '87' => array('conditions' => '87', 'account' => 'ZOR'), // Express
        '88' => array('conditions' => '88', 'account' => 'ZOR'), // Air Express
        '89' => array('conditions' => '89', 'account' => 'ZOR'), // Less Container Load
        '90' => array('conditions' => '90', 'account' => 'ZOR'), // Full Container Load
        '91' => array('conditions' => '91', 'account' => 'ZOR'), // Less Trailer Load Ro
        '92' => array('conditions' => '92', 'account' => 'ZOR'), // Full Trailer Load Ro
        '93' => array('conditions' => '93', 'account' => 'ZOR'), // Courier
        '94' => array('conditions' => '94', 'account' => 'ZSO'), // Next day 7 am
        '95' => array('conditions' => '95', 'account' => 'ZSO'), // Next day 9 am
        '96' => array('conditions' => '96', 'account' => 'ZSO'), // Next day 10.30 am
        '97' => array('conditions' => '97', 'account' => 'ZSO'), // Next day 12 noon
        'A1' => array('conditions' => 'A1', 'account' => 'ZOR'), // Apex
        'A2' => array('conditions' => 'A2', 'account' => 'ZSO'), // UPS AirFrt 2 Day
        'A4' => array('conditions' => 'A4', 'account' => 'ZOR'), // Alleghany Plant Serv
        'AG' => array('conditions' => 'AG', 'account' => 'ZSO'), // UPS AirFrt Nxt Guar
        'AU' => array('conditions' => 'AU', 'account' => 'ZSO'), // UPS AirFrt Nxt Day
        'AX' => array('conditions' => 'AX', 'account' => 'ZOR'), // AXFLOW Agreement
        'B0' => array('conditions' => 'B0', 'account' => 'ZOR'), // Bestway Gnd
        'B1' => array('conditions' => 'B1', 'account' => 'ZSO'), // Bestway Nxt Day Air
        'B2' => array('conditions' => 'B2', 'account' => 'ZSO'), // Bestway 2 Day Air
        'B3' => array('conditions' => 'B3', 'account' => 'ZOR'), // Bestway
        'BT' => array('conditions' => 'BT', 'account' => 'ZOR'), // BI-Weekly Ship Thurs
        'C0' => array('conditions' => 'C0', 'account' => 'ZOR'), // 3rd Thurs of Month
        'C1' => array('conditions' => 'C1', 'account' => 'ZOR'), // Monday Only
        'C2' => array('conditions' => 'C2', 'account' => 'ZOR'), // Tuesday Only
        'C3' => array('conditions' => 'C3', 'account' => 'ZOR'), // Wednesday Only
        'C4' => array('conditions' => 'C4', 'account' => 'ZOR'), // Thursday Only
        'C5' => array('conditions' => 'C5', 'account' => 'ZOR'), // Friday Only
        'C6' => array('conditions' => 'C6', 'account' => 'ZOR'), // Tues/Frid Consol.
        'C7' => array('conditions' => 'C7', 'account' => 'ZOR'), // Mon/Thurs Consol.
        'C8' => array('conditions' => 'C8', 'account' => 'ZOR'), // 2nd&4th Friday of md
        'C9' => array('conditions' => 'C9', 'account' => 'ZOR'), // Bi-Weekly Shp Tues
        'CA' => array('conditions' => 'CA', 'account' => 'ZOR'), // First Wed of Month
        'CB' => array('conditions' => 'CB', 'account' => 'ZOR'), // Tuesday & Thursday
        'CI' => array('conditions' => 'CI', 'account' => 'ZOR'), // UPS AirFrt Con Int
        'D0' => array('conditions' => 'D0', 'account' => 'ZOR'), // DHL Ground
        'D1' => array('conditions' => 'D1', 'account' => 'ZOR'), // DHL Exp Dom -10:30am
        'D2' => array('conditions' => 'D2', 'account' => 'ZOR'), // DHL Exp Dom - 12:00
        'D3' => array('conditions' => 'D3', 'account' => 'ZOR'), // DHL Exp Dom - 3:00pm
        'D4' => array('conditions' => 'D4', 'account' => 'ZOR'), // DHL Exp Dom - 2 Day
        'D5' => array('conditions' => 'D5', 'account' => 'ZOR'), // Day&Ross
        'DA' => array('conditions' => 'DA', 'account' => 'ZOR'), // DHL GFwd Int AirFrt
        'DI' => array('conditions' => 'DI', 'account' => 'ZOR'), // DHL Exp Int Sml Pkg
        'DM' => array('conditions' => 'DM', 'account' => 'ZOR'), // Dallas Mavis
        'DO' => array('conditions' => 'DO', 'account' => 'ZOR'), // DHL GFwd Int OcnFrt
        'E1' => array('conditions' => 'E1', 'account' => 'ZOR'), // Eclipse
        'EI' => array('conditions' => 'EI', 'account' => 'ZOR'), // FedEx Exp Int Prty
        'EX' => array('conditions' => 'EX', 'account' => 'ZOR'), // Export Orders Std.
        'F0' => array('conditions' => 'F0', 'account' => 'ZOR'), // FedEx Express Gnd
        'F1' => array('conditions' => 'F1', 'account' => 'ZSO'), // FedEx AirFrt Nxt Day
        'F2' => array('conditions' => 'F2', 'account' => 'ZSO'), // FedEx AirFrt 2 Day
        'F3' => array('conditions' => 'F3', 'account' => 'ZSO'), // FedEx Express 2 Day
        'FC' => array('conditions' => 'FC', 'account' => 'ZOR'), // FedEx Ground -Canada
        'FE' => array('conditions' => 'FE', 'account' => 'ZOR'), // FedEx Economy
        'FO' => array('conditions' => 'FO', 'account' => 'ZOR'), // FedEx Express Std-PM
        'FP' => array('conditions' => 'FP', 'account' => 'ZOR'), // FedEx Exp Prty-AM
        'FS' => array('conditions' => 'FS', 'account' => 'ZOR'), // FedEx Express Saver
        'G1' => array('conditions' => 'G1', 'account' => 'ZSO'), // DHL GFwd Dom Nxt Air
        'G2' => array('conditions' => 'G2', 'account' => 'ZSO'), // DHL GFwd Dom 2 Air
        'GD' => array('conditions' => 'GD', 'account' => 'ZOR'), // GD CHOICE
        'H1' => array('conditions' => 'H1', 'account' => 'ZOR'), // BOC Air
        'H2' => array('conditions' => 'H2', 'account' => 'ZOR'), // Central Freight Line
        'H3' => array('conditions' => 'H3', 'account' => 'ZOR'), // Ceva Logistics
        'H4' => array('conditions' => 'H4', 'account' => 'ZOR'), // Dayton Frt Line
        'H5' => array('conditions' => 'H5', 'account' => 'ZOR'), // Dawes Transport
        'H6' => array('conditions' => 'H6', 'account' => 'ZOR'), // DOHRN
        'H7' => array('conditions' => 'H7', 'account' => 'ZOR'), // Echo Logistics
        'H8' => array('conditions' => 'H8', 'account' => 'ZOR'), // Kuehne-Nagel
        'H9' => array('conditions' => 'H9', 'account' => 'ZOR'), // Lakeville Motor Expr
        'IA' => array('conditions' => 'IA', 'account' => 'ZOR'), // UPS AirFrt Dir - Int
        'IE' => array('conditions' => 'IE', 'account' => 'ZOR'), // UPS Exp Frt - Int
        'IN' => array('conditions' => 'IN', 'account' => 'ZOR'), // Invoice only
        'IO' => array('conditions' => 'IO', 'account' => 'ZOR'), // UPS SCS Int Ocn Std
        'LT' => array('conditions' => 'LT', 'account' => 'ZOR'), // LTL/Truck Carriers
        'M1' => array('conditions' => 'M1', 'account' => 'ZOR'), // Midland
        'N0' => array('conditions' => 'N0', 'account' => 'ZOR'), // UPS Expedited
        'N1' => array('conditions' => 'N1', 'account' => 'ZOR'), // Consolidated Shp
        'N2' => array('conditions' => 'N2', 'account' => 'ZOR'), // Normal Truck
        'N3' => array('conditions' => 'N3', 'account' => 'ZOR'), // Truck and Ferry
        'N4' => array('conditions' => 'N4', 'account' => 'ZOR'), // Technician
        'N5' => array('conditions' => 'N5', 'account' => 'ZOR'), // Night Star Express
        'N6' => array('conditions' => 'N6', 'account' => 'ZOR'), // UPS Express Saver
        'N7' => array('conditions' => 'N7', 'account' => 'ZOR'), // UPS Standard
        'N8' => array('conditions' => 'N8', 'account' => 'ZOR'), // UPS Express
        'N9' => array('conditions' => 'N9', 'account' => 'ZOR'), // UPS Express Plus
        'NA' => array('conditions' => 'NA', 'account' => 'ZOR'), // TNT
        'NF' => array('conditions' => 'NF', 'account' => 'ZSO'), // UPS AirFrt Nxt Out
        'NG' => array('conditions' => 'NG', 'account' => 'ZOR'), // DHL Europe
        'NH' => array('conditions' => 'NH', 'account' => 'ZOR'), // DHL Non Europe
        'NI' => array('conditions' => 'NI', 'account' => 'ZOR'), // DHL Documents
        'NJ' => array('conditions' => 'NJ', 'account' => 'ZOR'), // DHL 9.00 AM
        'NK' => array('conditions' => 'NK', 'account' => 'ZOR'), // DHL12.00 pm
        'NL' => array('conditions' => 'NL', 'account' => 'ZOR'), // Free of Charge
        'P1' => array('conditions' => 'P1', 'account' => 'ZOR'), // PILOT Nxt Day Air
        'P2' => array('conditions' => 'P2', 'account' => 'ZOR'), // PILOT 2 Day Air
        'PA' => array('conditions' => 'PA', 'account' => 'ZOR'), // PILOT Int AirFrt
        'PO' => array('conditions' => 'PO', 'account' => 'ZOR'), // PILOT Int OceanFrt
        'R1' => array('conditions' => 'R1', 'account' => 'ZOR'), // Reimer
        'SA' => array('conditions' => 'SA', 'account' => 'ZOR'), // Pchenker Int Air Std
        'SO' => array('conditions' => 'SO', 'account' => 'ZOR'), // Schenker Int Ocean
        'T1' => array('conditions' => 'T1', 'account' => 'ZOR'), // N&M TRANSFER
        'T2' => array('conditions' => 'T2', 'account' => 'ZOR'), // Rotra
        'T3' => array('conditions' => 'T3', 'account' => 'ZOR'), // Standard Forwarding
        'T4' => array('conditions' => 'T4', 'account' => 'ZOR'), // TAX AIR
        'T5' => array('conditions' => 'T5', 'account' => 'ZOR'), // Unishippers
        'TC' => array('conditions' => 'TC', 'account' => 'ZOR'), // TCI Transport
        'TF' => array('conditions' => 'TF', 'account' => 'ZOR'), // Spec Equip/Flatbed
        'TH' => array('conditions' => 'TH', 'account' => 'ZOR'), // Special Generic Othe
        'TL' => array('conditions' => 'TL', 'account' => 'ZOR'), // FTL/Truck Carriers
        'TT' => array('conditions' => 'TT', 'account' => 'ZOR'), // Tryon Transport
        'U0' => array('conditions' => 'U0', 'account' => 'ZOR'), // UPS Gnd Commercial
        'U1' => array('conditions' => 'U1', 'account' => 'ZOR'), // UPS Nxt Day Air
        'U2' => array('conditions' => 'U2', 'account' => 'ZOR'), // UPS 2 Day Air
        'U3' => array('conditions' => 'U3', 'account' => 'ZOR'), // UPS 3 Day Select
        'UA' => array('conditions' => 'UA', 'account' => 'ZOR'), // UPS Nxt Day Air Erly
        'UC' => array('conditions' => 'UC', 'account' => 'ZOR'), // UPS Canada - Std
        'WA' => array('conditions' => 'WA', 'account' => 'ZOR'), // UPS WW Exp Plus CA
        'WC' => array('conditions' => 'WC', 'account' => 'ZOR'), // UPS WW Express CA
        'WD' => array('conditions' => 'WD', 'account' => 'ZOR'), // UPS WW Expedited CA
        'WE' => array('conditions' => 'WE', 'account' => 'ZOR'), // UPS WW Express
        'WP' => array('conditions' => 'WP', 'account' => 'ZOR'), // UPS WW Express Plus
        'WS' => array('conditions' => 'WS', 'account' => 'ZOR'), // UPS WW Saver
        'WV' => array('conditions' => 'WV', 'account' => 'ZOR'), // UPS WW Saver CA
        'WX' => array('conditions' => 'WX', 'account' => 'ZOR'), // UPS WW Expedited
        'X1' => array('conditions' => 'X1', 'account' => 'ZOR'), // Export Orders Spec 1
        'X2' => array('conditions' => 'X2', 'account' => 'ZOR'), // Export Orders Spec 2
        'X3' => array('conditions' => 'X3', 'account' => 'ZOR'), // Export Orders Spec 3
        'X4' => array('conditions' => 'X4', 'account' => 'ZOR'), // Export Orders Spl4
        'XA' => array('conditions' => 'XA', 'account' => 'ZOR'), // A Duie Pyle
        'XB' => array('conditions' => 'XB', 'account' => 'ZOR'), // AAA Cooper
        'XC' => array('conditions' => 'XC', 'account' => 'ZOR'), // ATI Trans
        'XD' => array('conditions' => 'XD', 'account' => 'ZOR'), // Averitt
        'XE' => array('conditions' => 'XE', 'account' => 'ZOR'), // Benton Freight
        'XF' => array('conditions' => 'XF', 'account' => 'ZOR'), // CH Robinson
        'XG' => array('conditions' => 'XG', 'account' => 'ZOR'), // Conway Southern
        'XH' => array('conditions' => 'XH', 'account' => 'ZOR'), // DSV Air and Sea
        'XI' => array('conditions' => 'XI', 'account' => 'ZOR'), // Estes
        'XJ' => array('conditions' => 'XJ', 'account' => 'ZOR'), // Lynden Air
        'XK' => array('conditions' => 'XK', 'account' => 'ZOR'), // Nippon Express
        'XL' => array('conditions' => 'XL', 'account' => 'ZOR'), // R&L Carrier
        'XM' => array('conditions' => 'XM', 'account' => 'ZOR'), // Roadway
        'XN' => array('conditions' => 'XN', 'account' => 'ZOR'), // SAIA
        'XO' => array('conditions' => 'XO', 'account' => 'ZOR'), // Southeastern
        'XP' => array('conditions' => 'XP', 'account' => 'ZOR'), // USF Holland
        'XR' => array('conditions' => 'XR', 'account' => 'ZOR'), // UTI Logistics
        'XS' => array('conditions' => 'XS', 'account' => 'ZOR'), // Vitran Express
        'XT' => array('conditions' => 'XT', 'account' => 'ZOR'), // Ward Trucking
        'XU' => array('conditions' => 'XU', 'account' => 'ZOR'), // Wilson
        'XV' => array('conditions' => 'XV', 'account' => 'ZOR'), // Yellow Roadway Corp.
        'XW' => array('conditions' => 'XW', 'account' => 'ZOR'), // Fedex Priority (LTL)
        'XX' => array('conditions' => 'XX', 'account' => 'ZOR'), // UPS Freight (LTL )
        'YA' => array('conditions' => 'YA', 'account' => 'ZOR'), // Canadian Freightways
        'YC' => array('conditions' => 'YC', 'account' => 'ZOR'), // Dicom
        'YE' => array('conditions' => 'YE', 'account' => 'ZOR'), // EPIC Transport
        'YG' => array('conditions' => 'YG', 'account' => 'ZOR'), // Guilbeault Transport
        'YI' => array('conditions' => 'YI', 'account' => 'ZOR'), // Kingsway/Cabano
        'YK' => array('conditions' => 'YK', 'account' => 'ZOR'), // Manitoulin
        'YM' => array('conditions' => 'YM', 'account' => 'ZOR'), // Purolator 9 a.m.
        'YO' => array('conditions' => 'YO', 'account' => 'ZOR'), // Purolator Air
        'YQ' => array('conditions' => 'YQ', 'account' => 'ZOR'), // Purolator Ground
        'YS' => array('conditions' => 'YS', 'account' => 'ZOR'), // Robert Transport
        'YU' => array('conditions' => 'YU', 'account' => 'ZOR'), // Sameday air
        'YW' => array('conditions' => 'YW', 'account' => 'ZOR'), // Sameday ground
        'YY' => array('conditions' => 'YY', 'account' => 'ZOR'), // TST Transport
        'Z1' => array('conditions' => 'Z1', 'account' => 'ZOR'), // Hellmans
        'Z2' => array('conditions' => 'Z2', 'account' => 'ZOR'), // XPO Logistics 500430
        'Z3' => array('conditions' => 'Z3', 'account' => 'ZOR'), // Jarrett Logist500428
        'Z4' => array('conditions' => 'Z4', 'account' => 'ZOR'), // ESHIPPING 500429
        'Z5' => array('conditions' => 'Z5', 'account' => 'ZOR'), // Best Transpt.500427
        'Z9' => array('conditions' => 'Z9', 'account' => 'ZOR'), // Monthly 4th Thursday
        'ZL' => array('conditions' => 'ZL', 'account' => 'ZOR'), // Rockford M/W/F Consl
        'ZM' => array('conditions' => 'ZM', 'account' => 'ZOR'), // Rockford Tu/Th Consl
        'ZN' => array('conditions' => 'ZN', 'account' => 'ZOR'), // Rockford We/Fr Consl
        'ZO' => array('conditions' => 'ZO', 'account' => 'ZOR'), // Rockford Bi-Wkly Wed
        'ZS' => array('conditions' => 'ZS', 'account' => 'ZOR'), // Non Priority
        'ZZ' => array('conditions' => 'ZZ', 'account' => 'ZOR'), // To be informed
    );
}

/**
 * Map shipping method preference to SAP shipping conditions and account
 *
 * @param string $shipping_method_preference The shipping method from checkout
 * @return array Array with 'conditions' and 'account' keys
 */
function hytec_map_shipping_method_to_sap($shipping_method_preference) {
    $mapping = hytec_get_shipping_method_mapping();

    // Clean the input
    $method_code = trim($shipping_method_preference);

    // Check if we have a direct mapping
    if (isset($mapping[$method_code])) {
        return $mapping[$method_code];
    }

    // Legacy mappings for backward compatibility
    $legacy_mapping = array(
        'CC' => array('conditions' => '18', 'account' => 'ZSO'), // Customer Collect -> UPS Next Day Air
        'C' => array('conditions' => '18', 'account' => 'ZSO'),
        'customer_collect' => array('conditions' => '18', 'account' => 'ZSO'),
        'collect' => array('conditions' => '18', 'account' => 'ZSO'),
        'pickup' => array('conditions' => '69', 'account' => 'ZOR'), // Customer Pickup
        'standard' => array('conditions' => '1', 'account' => 'ZOR'), // Standard - Generic
        'express' => array('conditions' => '18', 'account' => 'ZSO'), // UPS Next Day Air
        'overnight' => array('conditions' => '18', 'account' => 'ZSO'), // UPS Next Day Air
    );

    // Check case-insensitive legacy mapping
    $upper_method = strtoupper($method_code);
    $lower_method = strtolower($method_code);

    if (isset($legacy_mapping[$upper_method])) {
        return $legacy_mapping[$upper_method];
    }

    if (isset($legacy_mapping[$lower_method])) {
        return $legacy_mapping[$lower_method];
    }

    // If no mapping found, default to UPS Next Day Air
    $default = array('conditions' => '18', 'account' => 'ZSO');
    hytec_log_sap_checkout("Unknown shipping method preference, using default", array(
        'input_method' => $shipping_method_preference,
        'default_used' => $default
    ));
    return $default;
}

/**
 * Hook to modify cart item prices with SAP pricing before checkout
 */
add_action('woocommerce_before_calculate_totals', 'hytec_apply_sap_pricing_to_cart', 10, 1);
function hytec_apply_sap_pricing_to_cart($cart) {
    // Only process for logged-in users and not in admin
    if (!is_user_logged_in() || is_admin()) {
        return;
    }

    // Avoid infinite loops
    if (did_action('woocommerce_before_calculate_totals') >= 2) {
        return;
    }

    $user_id = get_current_user_id();

    foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
        $product = $cart_item['data'];
        if (!($product instanceof WC_Product)) {
            continue;
        }

        // Check if we've already applied SAP pricing to this cart item
        if (isset($cart_item['sap_pricing_applied'])) {
            continue;
        }

        $quantity = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

        hytec_log_sap_checkout("Applying SAP pricing to cart item", array(
            'product_id' => $product->get_id(),
            'sku' => $product->get_sku(),
            'quantity' => $quantity,
            'original_price' => $product->get_price()
        ));

        // Get SAP pricing for this product with actual quantity
        if (function_exists('hytec_sap_get_or_fetch_pricing_with_quantity')) {
            $pricing = hytec_sap_get_or_fetch_pricing_with_quantity($product, $user_id, $quantity);

            if (is_array($pricing) && isset($pricing['net_value']) && $pricing['net_value'] !== '') {
                // Convert SAP response (divide by 100)
                $sap_total_price = (float)$pricing['net_value'] / 100;
                $sap_unit_price = $sap_total_price / $quantity;

                hytec_log_sap_checkout("Setting SAP price on cart item", array(
                    'sku' => $product->get_sku(),
                    'sap_unit_price' => $sap_unit_price,
                    'sap_total_price' => $sap_total_price
                ));

                // Set the SAP unit price on the product
                $product->set_price($sap_unit_price);

                // Store SAP pricing in cart item for later use during order creation
                WC()->cart->cart_contents[$cart_item_key]['sap_pricing_applied'] = true;
                WC()->cart->cart_contents[$cart_item_key]['sap_unit_price'] = $sap_unit_price;
                WC()->cart->cart_contents[$cart_item_key]['sap_total_price'] = $sap_total_price;
                WC()->cart->cart_contents[$cart_item_key]['sap_net_value'] = $pricing['net_value'];
                WC()->cart->cart_contents[$cart_item_key]['original_wc_price'] = $product->get_regular_price();

                // Also store in session as backup
                $session_sap_data = WC()->session->get('sap_pricing_data', array());
                $session_sap_data[$product->get_sku()] = array(
                    'unit_price' => $sap_unit_price,
                    'total_price' => $sap_total_price,
                    'net_value' => $pricing['net_value'],
                    'quantity' => $quantity
                );
                WC()->session->set('sap_pricing_data', $session_sap_data);

            } else {
                hytec_log_sap_checkout("SAP pricing not available for cart item", array(
                    'sku' => $product->get_sku(),
                    'pricing_response' => $pricing
                ));
            }
        }
    }
}

/**
 * Alternative approach: Hook into cart item price calculation
 */
add_filter('woocommerce_cart_item_price', 'hytec_modify_cart_item_display_price', 10, 3);
function hytec_modify_cart_item_display_price($price, $cart_item, $cart_item_key) {
    if (!is_user_logged_in()) {
        return $price;
    }

    if (isset($cart_item['sap_unit_price'])) {
        return wc_price($cart_item['sap_unit_price']);
    }

    return $price;
}

/**
 * Hook to modify order line item prices with SAP pricing before order is saved
 */
add_action('woocommerce_checkout_create_order_line_item', 'hytec_set_sap_pricing_on_order_line_item', 10, 4);
function hytec_set_sap_pricing_on_order_line_item($item, $cart_item_key, $values, $order) {
    // Only process for logged-in users
    if (!is_user_logged_in()) {
        return;
    }

    // Check if SAP pricing has already been applied to this item
    if ($item->get_meta('_sap_pricing_applied')) {
        return;
    }

    $product = $values['data'];
    if (!($product instanceof WC_Product)) {
        return;
    }

    $quantity = isset($values['quantity']) ? max(1, intval($values['quantity'])) : 1;

    // Log basic processing info (keep minimal logging for production)
    hytec_log_sap_checkout("Processing line item for SAP pricing", array(
        'sku' => $product->get_sku(),
        'has_sap_pricing_in_cart' => isset($values['sap_pricing_applied']) ? 'yes' : 'no'
    ));

    // Check if SAP pricing was already calculated and stored in the cart
    if (isset($values['sap_pricing_applied']) && $values['sap_pricing_applied'] === true) {
        // Use the SAP pricing that was calculated during cart/checkout
        $sap_unit_price = $values['sap_unit_price'];
        $sap_total_price = $values['sap_total_price'];
        $sap_net_value = $values['sap_net_value'];
        $original_wc_price = $values['original_wc_price'];

        hytec_log_sap_checkout("Using SAP pricing from cart", array(
            'sku' => $product->get_sku(),
            'sap_total_price' => $sap_total_price
        ));

        // Set the SAP prices on the order line item
        $item->set_props(array(
            'subtotal' => $sap_total_price,
            'total' => $sap_total_price,
        ));

        hytec_log_sap_checkout("SAP pricing applied to line item", array(
            'sku' => $product->get_sku()
        ));

        // Store SAP pricing data in line item meta for reference
        $item->add_meta_data('_sap_net_value', $sap_net_value, true);
        $item->add_meta_data('_sap_unit_price', $sap_unit_price, true);
        $item->add_meta_data('_sap_total_price', $sap_total_price, true);
        $item->add_meta_data('_pricing_source', 'SAP_from_cart', true);
        $item->add_meta_data('_sap_pricing_applied', 'yes', true);

        // Also store the original WooCommerce price for reference
        $item->add_meta_data('_original_wc_price', $original_wc_price, true);
        $item->add_meta_data('_original_wc_total', $original_wc_price * $quantity, true);

    } else {
        // Check session data as fallback
        $session_sap_data = WC()->session->get('sap_pricing_data', array());
        $product_sku = $product->get_sku();

        if (isset($session_sap_data[$product_sku])) {
            $sap_data = $session_sap_data[$product_sku];

            hytec_log_sap_checkout("Using SAP pricing from session", array(
                'sku' => $product_sku
            ));

            // Set the SAP prices on the order line item
            $item->set_props(array(
                'subtotal' => $sap_data['total_price'],
                'total' => $sap_data['total_price'],
            ));

            // Store SAP pricing data in line item meta
            $item->add_meta_data('_sap_net_value', $sap_data['net_value'], true);
            $item->add_meta_data('_sap_unit_price', $sap_data['unit_price'], true);
            $item->add_meta_data('_sap_total_price', $sap_data['total_price'], true);
            $item->add_meta_data('_pricing_source', 'SAP_from_session', true);
            $item->add_meta_data('_sap_pricing_applied', 'yes', true);

        } else {
            // No SAP pricing found anywhere, use WooCommerce pricing
            hytec_log_sap_checkout("No SAP pricing found in cart or session, using WooCommerce price", array(
                'sku' => $product->get_sku(),
                'wc_price' => $product->get_price()
            ));

            // Store that we're using WooCommerce pricing
            $item->add_meta_data('_pricing_source', 'WooCommerce_no_sap_found', true);
            $item->add_meta_data('_sap_pricing_applied', 'no', true);
        }
    }
}

/**
 * Hook to capture SAP pricing from checkout form submission
 */
add_action('woocommerce_checkout_process', 'hytec_capture_sap_pricing_from_checkout');
function hytec_capture_sap_pricing_from_checkout() {
    if (!is_user_logged_in()) {
        return;
    }

    hytec_log_sap_checkout("Capturing SAP pricing from checkout form", array(
        'post_data_keys' => array_keys($_POST)
    ));

    // Look for SAP pricing data in the POST data
    $sap_pricing_data = array();
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'sap_pricing_') === 0) {
            $sap_pricing_data[$key] = sanitize_text_field($value);
        }
    }

    if (!empty($sap_pricing_data)) {
        hytec_log_sap_checkout("Found SAP pricing data in checkout form", $sap_pricing_data);

        // Store SAP pricing data in session for use during order creation
        WC()->session->set('checkout_sap_pricing', $sap_pricing_data);
    } else {
        hytec_log_sap_checkout("No SAP pricing data found in checkout form");
    }
}

/**
 * Debug function to log cart and session data during checkout (disabled for production)
 */
// Disabled for production - uncomment for debugging
// add_action('woocommerce_checkout_process', 'hytec_debug_cart_and_session_data', 5);
function hytec_debug_cart_and_session_data() {
    // Debug function disabled for production
    /*
    if (!is_user_logged_in()) {
        return;
    }

    hytec_log_sap_checkout("=== DEBUGGING CART AND SESSION DATA ===");

    // Log cart contents
    $cart_debug = array();
    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
        $product = $cart_item['data'];
        $cart_debug[$cart_item_key] = array(
            'product_id' => $product->get_id(),
            'sku' => $product->get_sku(),
            'quantity' => $cart_item['quantity'],
            'price' => $product->get_price(),
            'has_sap_pricing_applied' => isset($cart_item['sap_pricing_applied']) ? 'yes' : 'no',
            'sap_unit_price' => isset($cart_item['sap_unit_price']) ? $cart_item['sap_unit_price'] : 'not_set',
            'sap_total_price' => isset($cart_item['sap_total_price']) ? $cart_item['sap_total_price'] : 'not_set'
        );
    }

    hytec_log_sap_checkout("Cart contents debug", $cart_debug);

    // Log session data
    $session_sap_data = WC()->session->get('sap_pricing_data', array());
    hytec_log_sap_checkout("Session SAP pricing data", $session_sap_data);
    */
}

/**
 * Hook to recalculate order totals after SAP pricing has been applied to line items
 */
add_action('woocommerce_checkout_order_processed', 'hytec_recalculate_order_totals_with_sap_pricing', 10, 3);
add_action('woocommerce_checkout_create_order', 'hytec_ensure_sap_pricing_on_order_creation', 20, 2);
add_action('woocommerce_checkout_order_processed', 'hytec_final_sap_pricing_check', 5, 3);
/**
 * Final check to ensure SAP pricing is correctly applied to order totals
 */
function hytec_final_sap_pricing_check($order_id, $posted_data, $order) {
    if (!is_user_logged_in()) {
        return;
    }

    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }

    hytec_log_sap_checkout("Final SAP pricing check", array(
        'order_id' => $order_id,
        'current_total' => $order->get_total()
    ));

    $sap_total = 0;
    $has_sap_items = false;

    // Calculate what the total should be based on SAP line items
    foreach ($order->get_items() as $item_id => $item) {
        $sap_pricing_applied = $item->get_meta('_sap_pricing_applied');

        if ($sap_pricing_applied === 'yes') {
            $has_sap_items = true;
            $sap_total += (float)$item->get_total();
        }
    }

    if ($has_sap_items) {
        // Add shipping and other fees to the SAP total
        $sap_total += (float)$order->get_shipping_total();
        $sap_total += (float)$order->get_total_fees();
        $sap_total += (float)$order->get_total_tax();

        // If the current order total doesn't match our SAP calculation, fix it
        if (abs($order->get_total() - $sap_total) > 0.01) {
            hytec_log_sap_checkout("Order total mismatch detected, correcting", array(
                'order_id' => $order_id,
                'current_total' => $order->get_total(),
                'expected_sap_total' => $sap_total
            ));

            $order->set_total($sap_total);
            $order->save();
        }
    }
}

function hytec_recalculate_order_totals_with_sap_pricing($order_id, $posted_data, $order) {
    // Only process for logged-in users
    if (!is_user_logged_in()) {
        return;
    }

    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }

    hytec_log_sap_checkout("Recalculating order totals with SAP pricing", array(
        'order_id' => $order_id,
        'original_total' => $order->get_total()
    ));

    $sap_subtotal = 0;
    $has_sap_pricing = false;

    // Calculate new subtotal based on SAP-priced line items
    foreach ($order->get_items() as $item_id => $item) {
        $pricing_source = $item->get_meta('_pricing_source');
        $sap_pricing_applied = $item->get_meta('_sap_pricing_applied');

        // Check if this item uses SAP pricing (either by pricing source or sap_pricing_applied flag)
        $uses_sap_pricing = ($sap_pricing_applied === 'yes') ||
                           (strpos($pricing_source, 'SAP') !== false);

        if ($uses_sap_pricing) {
            $sap_total_price = $item->get_meta('_sap_total_price');
            if ($sap_total_price) {
                $sap_subtotal += (float)$sap_total_price;
                $has_sap_pricing = true;

                hytec_log_sap_checkout("Line item with SAP pricing", array(
                    'item_id' => $item_id,
                    'sku' => $item->get_product() ? $item->get_product()->get_sku() : 'N/A',
                    'sap_total' => $sap_total_price,
                    'pricing_source' => $pricing_source,
                    'sap_pricing_applied' => $sap_pricing_applied
                ));
            }
        } else {
            // Use the item's current total (WooCommerce price)
            $sap_subtotal += (float)$item->get_total();

            hytec_log_sap_checkout("Line item with WooCommerce pricing", array(
                'item_id' => $item_id,
                'sku' => $item->get_product() ? $item->get_product()->get_sku() : 'N/A',
                'wc_total' => $item->get_total(),
                'pricing_source' => $pricing_source,
                'sap_pricing_applied' => $sap_pricing_applied
            ));
        }
    }

    if ($has_sap_pricing) {
        // Update order totals
        $order->set_subtotal($sap_subtotal);

        // For now, set total = subtotal (no tax calculation)
        // You may need to add tax calculation here based on your requirements
        $order->set_total($sap_subtotal);

        // Save the order with new totals
        $order->save();

        hytec_log_sap_checkout("Updated order totals with SAP pricing", array(
            'order_id' => $order_id,
            'new_subtotal' => $sap_subtotal,
            'new_total' => $sap_subtotal
        ));

        // Store that this order uses SAP pricing
        update_post_meta($order_id, '_uses_sap_pricing', 'yes');
        update_post_meta($order_id, '_sap_calculated_total', $sap_subtotal);
        update_post_meta($order_id, '_pricing_type', 'SAP_NET');
        update_post_meta($order_id, '_sap_pricing_timestamp', current_time('mysql'));
    } else {
        hytec_log_sap_checkout("No SAP pricing applied to order", array(
            'order_id' => $order_id
        ));
        update_post_meta($order_id, '_uses_sap_pricing', 'no');
        update_post_meta($order_id, '_pricing_type', 'WooCommerce_Standard');
    }
}

/**
 * Ensure SAP pricing is properly applied during order creation
 */
function hytec_ensure_sap_pricing_on_order_creation($order, $data) {
    if (!is_user_logged_in()) {
        return;
    }

    hytec_log_sap_checkout("Ensuring SAP pricing on order creation", array(
        'order_id' => $order->get_id()
    ));

    $has_sap_pricing = false;
    $total_sap_value = 0;

    // Check each line item and ensure SAP pricing is properly applied
    foreach ($order->get_items() as $item_id => $item) {
        $sap_pricing_applied = $item->get_meta('_sap_pricing_applied');
        $sap_total_price = $item->get_meta('_sap_total_price');

        if ($sap_pricing_applied === 'yes' && $sap_total_price) {
            // Ensure the line item totals are set to SAP pricing
            $item->set_subtotal($sap_total_price);
            $item->set_total($sap_total_price);
            $item->save();

            $total_sap_value += (float)$sap_total_price;
            $has_sap_pricing = true;

            hytec_log_sap_checkout("Applied SAP pricing to line item", array(
                'item_id' => $item_id,
                'sku' => $item->get_product() ? $item->get_product()->get_sku() : 'N/A',
                'sap_total' => $sap_total_price
            ));
        }
    }

    if ($has_sap_pricing) {
        // Recalculate order totals based on the updated line items
        $order->calculate_totals(false); // false = don't recalculate taxes
        $order->save();

        hytec_log_sap_checkout("Order totals recalculated with SAP pricing", array(
            'order_id' => $order->get_id(),
            'new_total' => $order->get_total(),
            'sap_line_items_total' => $total_sap_value
        ));
    }
}

/**
 * Display SAP pricing information in WooCommerce admin order details
 */
add_action('woocommerce_admin_order_data_after_order_details', 'hytec_display_sap_pricing_in_admin');
function hytec_display_sap_pricing_in_admin($order) {
    $uses_sap_pricing = get_post_meta($order->get_id(), '_uses_sap_pricing', true);
    $sap_calculated_total = get_post_meta($order->get_id(), '_sap_calculated_total', true);

    if ($uses_sap_pricing === 'yes') {
        echo '<div class="order_data_column" style="clear:both; padding-top: 20px;">';
        echo '<h3 style="color: #46b450;">SAP NET Pricing Information</h3>';
        echo '<div style="background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #46b450;">';
        echo '<p><strong style="color: #46b450;">✓ This order uses SAP NET pricing</strong></p>';

        if ($sap_calculated_total) {
            echo '<p><strong>SAP Calculated Total:</strong> ' . wc_price($sap_calculated_total) . '</p>';
        }

        // Count how many line items use SAP pricing
        $sap_items_count = 0;
        $total_items_count = 0;
        foreach ($order->get_items() as $item) {
            $total_items_count++;
            if ($item->get_meta('_sap_pricing_applied') === 'yes') {
                $sap_items_count++;
            }
        }

        echo '<p><strong>Line Items with SAP Pricing:</strong> ' . $sap_items_count . ' of ' . $total_items_count . '</p>';
        echo '<p><em>All prices shown are NET prices from SAP, unique to this customer.</em></p>';
        echo '</div>';
        echo '</div>';
    }
}

/**
 * Add SAP pricing notice to order totals section in admin
 */
add_action('woocommerce_admin_order_totals_after_total', 'hytec_display_sap_pricing_notice_in_totals');
function hytec_display_sap_pricing_notice_in_totals($order_id) {
    $uses_sap_pricing = get_post_meta($order_id, '_uses_sap_pricing', true);

    if ($uses_sap_pricing === 'yes') {
        echo '<tr>';
        echo '<td class="label" style="color: #46b450; font-weight: bold;">SAP NET Pricing:</td>';
        echo '<td width="1%"></td>';
        echo '<td class="total" style="color: #46b450; font-weight: bold;">Applied ✓</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<td colspan="3" style="font-size: 11px; color: #666; font-style: italic; padding-top: 5px;">';
        echo 'Order totals calculated using customer-specific NET prices from SAP. Do not recalculate unless necessary.';
        echo '</td>';
        echo '</tr>';
    }
}

/**
 * Display SAP pricing details for each line item in admin
 */
add_action('woocommerce_before_order_itemmeta', 'hytec_display_line_item_sap_pricing', 10, 3);
function hytec_display_line_item_sap_pricing($item_id, $item, $product) {
    if (!is_admin()) {
        return;
    }

    $pricing_source = $item->get_meta('_pricing_source');
    $sap_unit_price = $item->get_meta('_sap_unit_price');
    $sap_total_price = $item->get_meta('_sap_total_price');
    $sap_net_value = $item->get_meta('_sap_net_value');
    $original_wc_price = $item->get_meta('_original_wc_price');
    $sap_pricing_applied = $item->get_meta('_sap_pricing_applied');

    // Show SAP pricing information if available
    if ($sap_pricing_applied === 'yes' || $pricing_source) {
        echo '<div style="margin: 5px 0; padding: 8px; background: #e8f5e8; border-left: 4px solid #46b450; border-radius: 3px;">';
        echo '<strong style="color: #46b450;">✓ SAP NET Pricing Applied</strong><br>';

        if ($pricing_source) {
            echo '<strong>Source:</strong> ' . esc_html($pricing_source) . '<br>';
        }

        if ($sap_net_value) {
            // Display the raw SAP net value (before division by 100)
            echo '<strong>SAP Net Value (raw):</strong> ' . esc_html($sap_net_value) . '<br>';
        }

        if ($sap_unit_price) {
            echo '<strong>SAP Unit Price (NET):</strong> ' . wc_price($sap_unit_price) . '<br>';
        }

        if ($sap_total_price) {
            echo '<strong>SAP Total Price (NET):</strong> ' . wc_price($sap_total_price) . '<br>';
        }

        if ($original_wc_price) {
            echo '<strong>Original WC Price:</strong> ' . wc_price($original_wc_price) . ' <em style="color: #666;">(replaced by SAP NET)</em><br>';
        }

        // Show current line item totals for verification
        echo '<strong>Line Item Subtotal:</strong> ' . wc_price($item->get_subtotal()) . '<br>';
        echo '<strong>Line Item Total:</strong> ' . wc_price($item->get_total()) . '<br>';

        echo '</div>';
    } else {
        // Show when SAP pricing is not applied
        echo '<div style="margin: 5px 0; padding: 8px; background: #fff2cc; border-left: 4px solid #ffb900; border-radius: 3px;">';
        echo '<strong style="color: #ffb900;">⚠ Using WooCommerce Pricing</strong><br>';
        echo '<em>SAP NET pricing not applied to this item</em>';
        echo '</div>';
    }
}

/**
 * Prevent SAP pricing from being overwritten when admin recalculates order totals
 */
add_action('woocommerce_before_save_order_items', 'hytec_preserve_sap_pricing_on_admin_save', 10, 2);
add_action('woocommerce_process_shop_order_meta', 'hytec_preserve_sap_pricing_on_order_save', 5, 2);
function hytec_preserve_sap_pricing_on_admin_save($order_id, $items) {
    if (!is_admin() || !current_user_can('edit_shop_orders')) {
        return;
    }

    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }

    // Check if this order uses SAP pricing
    $uses_sap_pricing = get_post_meta($order_id, '_uses_sap_pricing', true);
    if ($uses_sap_pricing !== 'yes') {
        return;
    }

    // Preserve SAP pricing for each line item
    foreach ($order->get_items() as $item_id => $item) {
        $sap_pricing_applied = $item->get_meta('_sap_pricing_applied');
        if ($sap_pricing_applied === 'yes') {
            $sap_total_price = $item->get_meta('_sap_total_price');
            if ($sap_total_price) {
                // Ensure the line item totals remain at SAP pricing
                $item->set_subtotal($sap_total_price);
                $item->set_total($sap_total_price);
                $item->save();
            }
        }
    }
}

/**
 * Preserve SAP pricing when order is saved in admin
 */
function hytec_preserve_sap_pricing_on_order_save($order_id, $post) {
    if (!current_user_can('edit_shop_orders')) {
        return;
    }

    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }

    // Check if this order uses SAP pricing
    $uses_sap_pricing = get_post_meta($order_id, '_uses_sap_pricing', true);
    if ($uses_sap_pricing !== 'yes') {
        return;
    }

    hytec_log_sap_checkout("Preserving SAP pricing on admin order save", array(
        'order_id' => $order_id
    ));

    $sap_total = 0;
    $has_sap_items = false;

    // Ensure all SAP line items maintain their pricing
    foreach ($order->get_items() as $item_id => $item) {
        $sap_pricing_applied = $item->get_meta('_sap_pricing_applied');
        $sap_total_price = $item->get_meta('_sap_total_price');

        if ($sap_pricing_applied === 'yes' && $sap_total_price) {
            // Force the line item to use SAP pricing
            $item->set_subtotal($sap_total_price);
            $item->set_total($sap_total_price);
            $item->save();

            $sap_total += (float)$sap_total_price;
            $has_sap_items = true;
        }
    }

    if ($has_sap_items) {
        // Recalculate order total based on SAP line items
        $order->calculate_totals(false);
        $order->save();

        hytec_log_sap_checkout("SAP pricing preserved on admin save", array(
            'order_id' => $order_id,
            'sap_total' => $sap_total,
            'final_order_total' => $order->get_total()
        ));
    }
}

/**
 * Add diagnostic information to order notes when SAP pricing is applied
 */
add_action('woocommerce_checkout_order_processed', 'hytec_add_sap_pricing_order_note', 15, 3);
function hytec_add_sap_pricing_order_note($order_id, $posted_data, $order) {
    $uses_sap_pricing = get_post_meta($order_id, '_uses_sap_pricing', true);

    if ($uses_sap_pricing === 'yes') {
        $order = wc_get_order($order_id);
        if ($order) {
            $sap_items_count = 0;
            $total_sap_value = 0;

            foreach ($order->get_items() as $item) {
                if ($item->get_meta('_sap_pricing_applied') === 'yes') {
                    $sap_items_count++;
                    $sap_total = $item->get_meta('_sap_total_price');
                    if ($sap_total) {
                        $total_sap_value += floatval($sap_total);
                    }
                }
            }

            $note = sprintf(
                'SAP NET pricing applied to %d line item(s). Total SAP value: %s. Prices are customer-specific NET prices and should not be modified.',
                $sap_items_count,
                wc_price($total_sap_value)
            );

            $order->add_order_note($note, false, true);
        }
    }
}

/**
 * Hook to create SAP order after WooCommerce order is processed
 */
add_action('woocommerce_checkout_order_processed', 'hytec_process_sap_order_creation', 20, 3);
function hytec_process_sap_order_creation($order_id, $posted_data, $order) {
    hytec_log_sap_checkout("=== SAP ORDER CREATION HOOK TRIGGERED ===", array(
        'order_id' => $order_id,
        'hook' => 'woocommerce_checkout_order_processed'
    ));

    // Only process if we have all required data
    $purchase_order_number = get_post_meta($order_id, '_purchase_order_number', true);
    if (empty($purchase_order_number)) {
        hytec_log_sap_checkout("ERROR: No purchase order number found for order {$order_id}");
        update_post_meta($order_id, '_sap_order_error', 'Missing purchase order number');
        return;
    }

    hytec_log_sap_checkout("Starting SAP order creation process", array(
        'order_id' => $order_id,
        'purchase_order_number' => $purchase_order_number
    ));

    // Set processing status immediately
    update_post_meta($order_id, '_sap_order_processing', 'yes');

    // Create the SAP order
    $result = hytec_create_sap_sales_order($order_id);

    // Clear processing status
    delete_post_meta($order_id, '_sap_order_processing');

    if (is_wp_error($result)) {
        $error_message = $result->get_error_message();
        hytec_log_sap_checkout("SAP order creation FAILED for order {$order_id}", array(
            'error_code' => $result->get_error_code(),
            'error_message' => $error_message,
            'error_data' => $result->get_error_data()
        ));
        // Store the error for display on thank you page
        update_post_meta($order_id, '_sap_order_error', $error_message);
        update_post_meta($order_id, '_sap_order_status', 'failed');
    } else {
        hytec_log_sap_checkout("SAP order creation SUCCESSFUL for order {$order_id}", array(
            'result' => $result
        ));
        update_post_meta($order_id, '_sap_order_status', 'completed');
    }

    hytec_log_sap_checkout("=== SAP ORDER CREATION PROCESS COMPLETED ===", array(
        'order_id' => $order_id
    ));
}

/**
 * Add admin function to manually trigger SAP order creation for testing
 */
function hytec_manual_sap_order_creation() {
    if (!current_user_can('manage_options')) {
        return;
    }

    if (isset($_GET['test_sap_order']) && isset($_GET['order_id'])) {
        $order_id = intval($_GET['order_id']);
        $result = hytec_create_sap_sales_order($order_id);

        if (is_wp_error($result)) {
            echo '<div class="notice notice-error"><p>SAP Order Creation Failed: ' . esc_html($result->get_error_message()) . '</p></div>';
        } else {
            echo '<div class="notice notice-success"><p>SAP Order Creation Successful! Response: ' . esc_html(json_encode($result)) . '</p></div>';
        }
    }
}
add_action('admin_notices', 'hytec_manual_sap_order_creation');

/**
 * Add SAP Order Test menu to admin
 */
add_action('admin_menu', 'hytec_add_sap_order_test_menu');
function hytec_add_sap_order_test_menu() {
    add_submenu_page(
        'woocommerce',
        'SAP Order Test',
        'SAP Order Test',
        'manage_options',
        'sap-order-test',
        'hytec_sap_order_test_page'
    );

    add_submenu_page(
        'woocommerce',
        'SAP Checkout Logs',
        'SAP Checkout Logs',
        'manage_options',
        'sap-checkout-logs',
        'hytec_sap_checkout_logs_page'
    );

    add_submenu_page(
        'woocommerce',
        'Test Ship-To Format',
        'Test Ship-To Format',
        'manage_options',
        'test-shipto-format',
        'hytec_test_shipto_format_page'
    );

    add_submenu_page(
        'woocommerce',
        'Test SAP Pricing',
        'Test SAP Pricing',
        'manage_options',
        'test-sap-pricing',
        'hytec_test_sap_pricing_page'
    );
}

function hytec_sap_order_test_page() {
    ?>
    <div class="wrap">
        <h1>SAP Order Creation Test</h1>

        <?php if (isset($_POST['test_order_id'])): ?>
            <?php
            $order_id = intval($_POST['test_order_id']);
            $order = wc_get_order($order_id);

            if (!$order) {
                echo '<div class="notice notice-error"><p>Order not found!</p></div>';
            } else {
                echo '<h2>Testing Order #' . $order_id . '</h2>';

                // Show order details
                echo '<h3>Order Details:</h3>';
                echo '<ul>';
                echo '<li><strong>Purchase Order:</strong> ' . get_post_meta($order_id, '_purchase_order_number', true) . '</li>';
                echo '<li><strong>Customer Requested Ship Date:</strong> ' . get_post_meta($order_id, '_customer_requested_ship_date', true) . '</li>';
                echo '<li><strong>Shipping Method Preference:</strong> ' . get_post_meta($order_id, '_shipping_method_preference', true) . '</li>';
                echo '<li><strong>Shipping Account Number:</strong> ' . get_post_meta($order_id, '_shipping_account_number', true) . '</li>';
                echo '<li><strong>Special Requests:</strong> ' . get_post_meta($order_id, '_special_requests', true) . '</li>';
                echo '<li><strong>User ID:</strong> ' . $order->get_user_id() . '</li>';
                echo '<li><strong>Selected Address ID (_wcmca_shipping_selected_address_id):</strong> ' . get_post_meta($order_id, '_wcmca_shipping_selected_address_id', true) . '</li>';
                echo '<li><strong>Custom Shipping Address ID (_custom_shipping_address_id):</strong> ' . get_post_meta($order_id, '_custom_shipping_address_id', true) . '</li>';
                echo '<li><strong>WCMCA Shipping Address (_wcmca_shipping_address):</strong> ' . get_post_meta($order_id, '_wcmca_shipping_address', true) . '</li>';
                echo '</ul>';

                // Show custom shipping address data if available
                $custom_address_data = get_post_meta($order_id, '_custom_shipping_address_data', true);
                if (!empty($custom_address_data)) {
                    echo '<h4>Custom Shipping Address Data:</h4>';
                    echo '<pre style="background: #f0f0f0; padding: 10px; overflow-x: auto;">';
                    echo esc_html(print_r($custom_address_data, true));
                    echo '</pre>';
                }

                // Show customer data
                $user_id = $order->get_user_id();
                if ($user_id) {
                    $main_user_id = get_main_b2b_admin_id($user_id);
                    $customer_data = hytec_get_sap_customer_for_user($main_user_id);

                    echo '<h3>Customer Data:</h3>';
                    if ($customer_data) {
                        echo '<ul>';
                        echo '<li><strong>Raw Customer ID:</strong> ' . $customer_data->customer_id . '</li>';
                        echo '<li><strong>Formatted Customer ID:</strong> ' . hytec_format_customer_id($customer_data->customer_id) . '</li>';
                        echo '<li><strong>Company Code:</strong> ' . ($customer_data->company_code ?? 'N/A') . '</li>';
                        echo '<li><strong>Z7_Partner_no:</strong> ' . ($customer_data->Z7_Partner_no ?? 'N/A') . '</li>';
                        if (!empty($customer_data->Z7_Partner_no)) {
                            echo '<li><strong>Formatted Z7_Partner_no:</strong> ' . hytec_format_customer_id($customer_data->Z7_Partner_no) . '</li>';
                        }
                        echo '</ul>';
                    } else {
                        echo '<p style="color: red;">No customer data found!</p>';
                    }
                }

                // Test the payload building
                echo '<h3>Testing Payload Building:</h3>';
                $payload = hytec_build_sap_order_payload($order);

                if (is_wp_error($payload)) {
                    echo '<div class="notice notice-error"><p>Payload Error: ' . $payload->get_error_message() . '</p></div>';
                } else {
                    echo '<pre style="background: #f0f0f0; padding: 10px; overflow-x: auto;">';
                    echo esc_html(json_encode($payload, JSON_PRETTY_PRINT));
                    echo '</pre>';

                    // Test the actual API call
                    if (isset($_POST['make_api_call'])) {
                        echo '<h3>API Call Result:</h3>';
                        $result = hytec_create_sap_sales_order($order_id);

                        if (is_wp_error($result)) {
                            echo '<div class="notice notice-error"><p>API Error: ' . $result->get_error_message() . '</p></div>';
                        } else {
                            echo '<div class="notice notice-success"><p>API Call Successful!</p></div>';
                            echo '<pre style="background: #f0f0f0; padding: 10px; overflow-x: auto;">';
                            echo esc_html(json_encode($result, JSON_PRETTY_PRINT));
                            echo '</pre>';
                        }

                        // Show current order meta after API call
                        echo '<h3>Order Meta After API Call:</h3>';
                        echo '<ul>';
                        echo '<li><strong>_sap_order_response:</strong> ' . (get_post_meta($order_id, '_sap_order_response', true) ? 'Present' : 'Not set') . '</li>';
                        echo '<li><strong>_sap_order_response_code:</strong> ' . get_post_meta($order_id, '_sap_order_response_code', true) . '</li>';
                        echo '<li><strong>_sap_order_created:</strong> ' . get_post_meta($order_id, '_sap_order_created', true) . '</li>';
                        echo '<li><strong>_sap_order_error:</strong> ' . get_post_meta($order_id, '_sap_order_error', true) . '</li>';
                        echo '<li><strong>_sap_order_status:</strong> ' . get_post_meta($order_id, '_sap_order_status', true) . '</li>';
                        echo '<li><strong>_sap_order_request:</strong> ' . (get_post_meta($order_id, '_sap_order_request', true) ? 'Present' : 'Not set') . '</li>';
                        echo '</ul>';

                        // Show the request JSON if available
                        $request_json = get_post_meta($order_id, '_sap_order_request', true);
                        if (!empty($request_json)) {
                            echo '<h4>Request JSON Sent to SAP:</h4>';
                            echo '<pre style="background: #f0f0f0; padding: 10px; overflow-x: auto; border-left: 3px solid #007cba;">';
                            echo esc_html($request_json);
                            echo '</pre>';
                        } else {
                            echo '<p style="color: red;">No request JSON found in order meta!</p>';

                            // Debug: Show all meta keys for this order
                            $all_meta = get_post_meta($order_id);
                            echo '<h4>All Order Meta Keys:</h4>';
                            echo '<ul>';
                            foreach ($all_meta as $key => $value) {
                                if (strpos($key, 'sap') !== false) {
                                    echo '<li><strong>' . esc_html($key) . ':</strong> ' . esc_html(is_array($value) ? json_encode($value) : $value[0]) . '</li>';
                                }
                            }
                            echo '</ul>';
                        }
                    }

                    // Handle retry SAP order creation
                    if (isset($_POST['retry_sap_order'])) {
                        echo '<h3>Retrying SAP Order Creation:</h3>';

                        // Clear existing meta
                        delete_post_meta($order_id, '_sap_order_response');
                        delete_post_meta($order_id, '_sap_order_response_code');
                        delete_post_meta($order_id, '_sap_order_created');
                        delete_post_meta($order_id, '_sap_order_error');
                        delete_post_meta($order_id, '_sap_order_status');
                        delete_post_meta($order_id, '_sap_order_processing');

                        // Trigger the order creation process
                        hytec_process_sap_order_creation($order_id, array(), $order);

                        echo '<div class="notice notice-info"><p>SAP order creation process triggered. Check the logs and order meta above.</p></div>';
                    }

                    // Handle manual request JSON retrieval
                    if (isset($_POST['get_request_json'])) {
                        echo '<h3>Manual Request JSON Retrieval:</h3>';

                        // Try to rebuild the request from the order
                        $payload = hytec_build_sap_order_payload($order);
                        if (is_wp_error($payload)) {
                            echo '<div class="notice notice-error"><p>Error building payload: ' . $payload->get_error_message() . '</p></div>';
                        } else {
                            echo '<h4>Rebuilt Request JSON:</h4>';
                            echo '<pre style="background: #f0f0f0; padding: 10px; overflow-x: auto; border-left: 3px solid #28a745;">';
                            echo esc_html(wp_json_encode($payload, JSON_PRETTY_PRINT));
                            echo '</pre>';

                            // Store it for future reference
                            update_post_meta($order_id, '_sap_order_request', wp_json_encode($payload, JSON_PRETTY_PRINT));
                            echo '<div class="notice notice-success"><p>Request JSON has been stored in order meta.</p></div>';
                        }
                    }
                }
            }
            ?>
        <?php endif; ?>

        <form method="post">
            <table class="form-table">
                <tr>
                    <th scope="row">Order ID</th>
                    <td>
                        <input type="number" name="test_order_id" value="<?php echo isset($_POST['test_order_id']) ? intval($_POST['test_order_id']) : ''; ?>" required />
                        <p class="description">Enter a WooCommerce order ID to test SAP order creation</p>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="test_payload" class="button-primary" value="Test Payload Building" />
                <input type="submit" name="make_api_call" class="button-secondary" value="Make Actual API Call" style="margin-left: 10px;" />
                <input type="submit" name="retry_sap_order" class="button-secondary" value="Retry SAP Order Creation" style="margin-left: 10px;" />
                <input type="submit" name="get_request_json" class="button-secondary" value="Get/Rebuild Request JSON" style="margin-left: 10px;" />
            </p>
        </form>
    </div>
    <?php
}

function hytec_sap_checkout_logs_page() {
    $log_file = WP_CONTENT_DIR . '/logs/checkout-sap.log';

    // Handle log clearing
    if (isset($_POST['clear_logs'])) {
        if (file_exists($log_file)) {
            file_put_contents($log_file, '');
            echo '<div class="notice notice-success"><p>Logs cleared successfully!</p></div>';
        }
    }

    // Handle log download
    if (isset($_GET['download_logs'])) {
        if (file_exists($log_file)) {
            header('Content-Type: text/plain');
            header('Content-Disposition: attachment; filename="checkout-sap-' . date('Y-m-d-H-i-s') . '.log"');
            readfile($log_file);
            exit;
        }
    }

    ?>
    <div class="wrap">
        <h1>SAP Checkout Logs</h1>

        <div style="margin-bottom: 20px;">
            <a href="<?php echo admin_url('admin.php?page=sap-checkout-logs&download_logs=1'); ?>" class="button">Download Logs</a>
            <form method="post" style="display: inline-block; margin-left: 10px;">
                <input type="submit" name="clear_logs" class="button button-secondary" value="Clear Logs" onclick="return confirm('Are you sure you want to clear all logs?');" />
            </form>
        </div>

        <?php
        if (file_exists($log_file)) {
            $log_content = file_get_contents($log_file);
            $log_size = filesize($log_file);

            echo '<p><strong>Log file:</strong> ' . $log_file . '</p>';
            echo '<p><strong>File size:</strong> ' . size_format($log_size) . '</p>';
            echo '<p><strong>Last modified:</strong> ' . date('Y-m-d H:i:s', filemtime($log_file)) . '</p>';

            if (!empty($log_content)) {
                // Show last 50 lines by default
                $lines = explode("\n", $log_content);
                $total_lines = count($lines);
                $show_lines = min(200, $total_lines);
                $recent_lines = array_slice($lines, -$show_lines);

                echo '<h3>Recent Log Entries (Last ' . $show_lines . ' lines of ' . $total_lines . ' total)</h3>';
                echo '<div style="background: #f1f1f1; padding: 15px; border-radius: 5px; max-height: 600px; overflow-y: auto;">';
                echo '<pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; font-size: 12px;">';
                echo esc_html(implode("\n", $recent_lines));
                echo '</pre>';
                echo '</div>';

                if ($total_lines > $show_lines) {
                    echo '<p><em>Showing last ' . $show_lines . ' lines. Download the full log file to see all entries.</em></p>';
                }
            } else {
                echo '<p>Log file is empty.</p>';
            }
        } else {
            echo '<p>Log file does not exist yet. It will be created when the first SAP order is processed.</p>';
        }
        ?>
    </div>
    <?php
}

    $table_name = $wpdb->prefix . 'sap_shipto_addresses';

    // Get addresses from database
    $record = $wpdb->get_row($wpdb->prepare(
        "SELECT wcmca_addresses_data, total_addresses, has_default_address, updated_at
         FROM {$table_name}
         WHERE customer_id = %s AND status = 'active'",
        $customer_id
    ));

    if (!$record) {
        return false;
    }

    $addresses = maybe_unserialize($record->wcmca_addresses_data);

    if (!is_array($addresses)) {
        return false;
    }

    return $addresses;
}

/**
 * Get a specific address by ID from the user's addresses
 *
 * @param int $user_id WordPress user ID
 * @param string $address_id Address ID to find
 * @return array|false Address data or false if not found
 */
function get_custom_address_by_id($user_id, $address_id) {
    $addresses = get_custom_user_addresses($user_id);

    if (!$addresses) {
        return false;
    }

    foreach ($addresses as $address) {
        if (isset($address['address_id']) && $address['address_id'] == $address_id) {
            return $address;
        }
    }

    return false;
}

/**
 * Create dropdown options HTML for address selector
 *
 * @param int $user_id WordPress user ID
 * @param string $type Address type ('billing' or 'shipping')
 * @return string HTML options for select dropdown
 */
function create_custom_address_dropdown_options($user_id, $type = 'shipping') {
    $addresses = get_custom_user_addresses($user_id);
    $options_html = '';

    // Default "none" option
    // $options_html .= '<option value="none">' . esc_html__('Select an address', 'woocommerce') . '</option>';

    if ($addresses && is_array($addresses)) {
        foreach ($addresses as $address) {
            // Only show addresses of the requested type
            if (isset($address['type']) && $address['type'] == $type) {
                $address_id = isset($address['address_id']) ? $address['address_id'] : '';
                $address_name = isset($address['address_internal_name']) ? $address['address_internal_name'] : '';

                // Check if this is the default address
                $is_default = isset($address[$type . '_is_default_address']) && $address[$type . '_is_default_address'] == '1';
                $default_text = $is_default ? ' (' . esc_html__('Default', 'woocommerce') . ')' : '';
                $default_class = $is_default ? ' class="wcmca_default_droppdown_option"' : '';
                $selected = $is_default ? ' selected="selected"' : '';

                if ($address_name && $address_id) {
                    $options_html .= '<option value="' . esc_attr($address_id) . '"' . $selected . $default_class . '>' .
                                   esc_html($address_name . $default_text) . '</option>';
                }
            }
        }
    }

    // Add "Add One Time Shipping Address" option at the end
    // if (!empty($addresses)) {
        $options_html .= '<option value="add_new">' . esc_html__('Add One Time Shipping Address', 'woocommerce') . '</option>';
    // }

    return $options_html;
}

/**
 * AJAX handler to get address data by ID
 */
function custom_get_address_by_id_ajax() {
    // Log the request for debugging
    error_log('Custom address AJAX request received');
    error_log('POST data: ' . print_r($_POST, true));

    // Verify nonce for security (allow fallback for testing)
    $security_token = $_POST['security_token'] ?? '';
    if ($security_token !== 'fallback_token' && !wp_verify_nonce($security_token, 'custom_address_security')) {
        error_log('Security check failed. Token: ' . $security_token);
        wp_send_json_error('Security check failed');
        return;
    }

    $address_id = sanitize_text_field($_POST['address_id'] ?? '');
    $user_id = get_current_user_id();

    error_log('Address ID: ' . $address_id . ', User ID: ' . $user_id);

    if (!$address_id || !$user_id) {
        error_log('Invalid request - missing address_id or user_id');
        wp_send_json_error('Invalid request');
        return;
    }

    $address = get_custom_address_by_id($user_id, $address_id);

    error_log('Found address: ' . print_r($address, true));

    if ($address) {
        wp_send_json_success($address);
    } else {
        wp_send_json_error('Address not found');
    }
}
add_action('wp_ajax_custom_get_address_by_id', 'custom_get_address_by_id_ajax');
add_action('wp_ajax_nopriv_custom_get_address_by_id', 'custom_get_address_by_id_ajax');

/**
 * Custom Ship To Addresses display for wp-admin user profile
 * This overrides the WooCommerce core function to use our new SAP ShipTo table structure
 */
function custom_display_ship_to_addresses( $user ) {
    if ( ! apply_filters( 'woocommerce_current_user_can_edit_customer_meta_fields', current_user_can( 'manage_woocommerce' ), $user->ID ) ) {
        return;
    }

    // Get addresses from wp_sap_shipto_addresses table using the new structure
    $shipping_addresses = array();

    // Try to get addresses from the new SAP ShipTo table first
    if ( function_exists( 'get_custom_user_addresses' ) ) {
        $sap_addresses = get_custom_user_addresses( $user->ID );

        if ( is_array( $sap_addresses ) ) {
            // Filter for shipping addresses only and reformat for display
            foreach ( $sap_addresses as $address ) {
                if ( isset( $address['type'] ) && $address['type'] === 'shipping' ) {
                    $address_id = $address['address_id'] ?? uniqid();
                    $shipping_addresses[$address_id] = $address;
                }
            }
        }
    }

    // Fallback to old _wcmca_additional_addresses metadata if no SAP addresses found
    if ( empty( $shipping_addresses ) ) {
        $addresses = get_user_meta( $user->ID, '_wcmca_additional_addresses', true );

        if ( is_array( $addresses ) ) {
            // Filter for shipping addresses only
            foreach ( $addresses as $address_id => $address ) {
                if ( isset( $address['type'] ) && $address['type'] === 'shipping' ) {
                    $shipping_addresses[$address_id] = $address;
                }
            }
        }
    }

    ?>
    <h2><?php esc_html_e( 'Ship To Addresses', 'woocommerce' ); ?> <span style="color: #0073aa;">(SAP ShipTo)</span></h2>
    <table class="form-table" id="ship-to-addresses-table">
        <tbody>
            <?php if ( empty( $shipping_addresses ) ): ?>
                <tr>
                    <td colspan="2">
                        <p class="description"><?php esc_html_e( 'No Ship To addresses found for this user.', 'woocommerce' ); ?></p>
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ( $shipping_addresses as $address_id => $address ): ?>
                    <tr class="ship-to-address-row" data-address-id="<?php echo esc_attr( $address_id ); ?>">
                        <th scope="row">
                            <strong><?php echo esc_html( $address['address_internal_name'] ?? 'Unnamed Address' ); ?></strong>
                            <?php if ( isset( $address['shipping_is_default_address'] ) && $address['shipping_is_default_address'] ): ?>
                                <span class="ship-to-default-badge" style="background: #0073aa; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-left: 5px;">
                                    <?php esc_html_e( 'Default', 'woocommerce' ); ?>
                                </span>
                            <?php endif; ?>
                            <br>
                            <small style="color: #666;">Address ID: <?php echo esc_html( $address_id ); ?></small>
                        </th>
                        <td>
                            <div class="ship-to-address-details">
                                <?php if ( ! empty( $address['shipping_company'] ) ): ?>
                                    <div><strong><?php echo esc_html( $address['shipping_company'] ); ?></strong></div>
                                <?php endif; ?>

                                <?php if ( ! empty( $address['shipping_address_1'] ) ): ?>
                                    <div><?php echo esc_html( $address['shipping_address_1'] ); ?></div>
                                <?php endif; ?>

                                <?php if ( ! empty( $address['shipping_address_2'] ) ): ?>
                                    <div><?php echo esc_html( $address['shipping_address_2'] ); ?></div>
                                <?php endif; ?>

                                <div>
                                    <?php echo esc_html( $address['shipping_city'] ?? '' ); ?>
                                    <?php if ( ! empty( $address['shipping_state'] ) ): ?>
                                        , <?php echo esc_html( $address['shipping_state'] ); ?>
                                    <?php endif; ?>
                                    <?php echo esc_html( $address['shipping_postcode'] ?? '' ); ?>
                                </div>

                                <?php if ( ! empty( $address['shipping_country'] ) ): ?>
                                    <div><?php echo esc_html( WC()->countries->countries[ $address['shipping_country'] ] ?? $address['shipping_country'] ); ?></div>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    <style>
        .ship-to-address-details {
            line-height: 1.4;
        }
        .ship-to-address-details div {
            margin-bottom: 2px;
        }
    </style>
    <?php
}

// Hook into the user profile display to replace the default Ship To Addresses section
add_action( 'show_user_profile', 'custom_display_ship_to_addresses', 30 );
add_action( 'edit_user_profile', 'custom_display_ship_to_addresses', 30 );

// Remove the original WooCommerce Ship To Addresses display to avoid duplication
add_action( 'init', function() {
    if ( class_exists( 'WC_Admin_Profile' ) ) {
        $wc_admin_profile = new WC_Admin_Profile();
        remove_action( 'show_user_profile', array( $wc_admin_profile, 'display_ship_to_addresses' ), 20 );
        remove_action( 'edit_user_profile', array( $wc_admin_profile, 'display_ship_to_addresses' ), 20 );
    }
}, 20 );

/**
 * Replace WCMCA address dropdown with custom implementation
 */
function render_custom_address_select_menu($type = 'shipping') {
    $user_id = get_current_user_id();

    if (!$user_id) {
        return;
    }

    // Enqueue necessary styles and scripts
    wp_enqueue_style('select2');
    wp_enqueue_script('select2');

    $options_html = create_custom_address_dropdown_options($user_id, $type);

    ?>
    <p class="form-row form-row custom_address_selector_container">
        <label><?php esc_html_e('Select an address', 'woocommerce'); ?></label>
        <select class="custom_address_select_menu" data-type="<?php echo esc_attr($type); ?>"
                id="custom_address_select_menu_<?php echo esc_attr($type); ?>"
                name="custom_<?php echo esc_attr($type); ?>_selected_address_id">
            <?php echo $options_html; ?>
        </select>
    </p>

    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Initialize Select2 for the custom address dropdown
        $('.custom_address_select_menu').selectWoo({
            width: 'resolve',
            containerCssClass: "custom-address-select-menu-container",
            dropdownCssClass: "custom-address-select-menu-dropdown"
        });
    });
    </script>
    <label>Location name</label>
    <?php
}

/**
 * Hook into checkout to replace WCMCA address dropdown
 */
function add_custom_shipping_address_select_menu($checkout) {
    if (!get_current_user_id()) {
        return;
    }

    render_custom_address_select_menu('shipping');
}
add_action('woocommerce_before_checkout_shipping_form', 'add_custom_shipping_address_select_menu', 5);

/**
 * Enqueue custom address JavaScript
 */
function enqueue_custom_address_scripts() {
    if (is_checkout()) {
        // Add version based on file modification time for cache busting
        $version = filemtime(get_stylesheet_directory() . '/assets/js/custom-address-checkout.js');

        wp_enqueue_script('custom-address-checkout', get_stylesheet_directory_uri() . '/assets/js/custom-address-checkout.js', array('jquery'), $version, true);

        // Localize script with AJAX URL and nonce
        wp_localize_script('custom-address-checkout', 'customAddress', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'security_token' => wp_create_nonce('custom_address_security'),
            'debug' => current_user_can('administrator') // Add debug flag for admins
        ));

        // Also add the global ajaxurl for fallback
        wp_localize_script('custom-address-checkout', 'ajaxurl', admin_url('admin-ajax.php'));
    }
}
add_action('wp_enqueue_scripts', 'enqueue_custom_address_scripts');

/**
 * Add custom CSS for address dropdown
 */
function add_custom_address_styles() {
    if (is_checkout()) {
        ?>
        <style>
        .custom_address_selector_container {
            margin-bottom: 20px;
        }

        .custom_address_selector_container.loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .custom-address-loading {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }

        .custom-address-select-menu-container {
            width: 100% !important;
        }

        .custom_address_select_menu {
            width: 100%;
            max-width: 100%;
        }

        .wcmca_default_droppdown_option {
            font-weight: bold;
        }

        /* Hide WCMCA plugin dropdown if it still appears */
        .wcmca_address_selector_container,
        .wcmca_address_select_menu {
            display: none !important;
        }
        </style>
        <?php
    }
}
add_action('wp_head', 'add_custom_address_styles');

/**
 * Remove WCMCA plugin hooks to prevent conflicts
 */
function remove_wcmca_hooks() {
    // Remove WCMCA checkout hooks
    remove_action('woocommerce_before_checkout_shipping_form', array('WCMCA_CheckoutPage', 'add_shipping_address_select_menu'));
    remove_action('woocommerce_before_checkout_billing_form', array('WCMCA_CheckoutPage', 'add_billing_address_select_menu'));

    // Try to remove with different priority
    remove_action('woocommerce_before_checkout_shipping_form', 'add_shipping_address_select_menu', 10);
    remove_action('woocommerce_before_checkout_billing_form', 'add_billing_address_select_menu', 10);
}
add_action('init', 'remove_wcmca_hooks', 20);

/**
 * Save custom address selection during checkout
 */
function save_custom_address_selection($order_id) {
    if (isset($_POST['custom_shipping_selected_address_id'])) {
        $selected_address_id = sanitize_text_field($_POST['custom_shipping_selected_address_id']);

        if ($selected_address_id && $selected_address_id !== 'none' && $selected_address_id !== 'add_new') {
            // Save the selected address ID to order meta (multiple formats for compatibility)
            update_post_meta($order_id, '_custom_shipping_address_id', $selected_address_id);
            update_post_meta($order_id, '_wcmca_shipping_selected_address_id', $selected_address_id);
            update_post_meta($order_id, '_wcmca_shipping_address', $selected_address_id);

            // Get the full address data and save it for reference
            $user_id = get_current_user_id();
            $address_data = get_custom_address_by_id($user_id, $selected_address_id);

            if ($address_data) {
                update_post_meta($order_id, '_custom_shipping_address_data', $address_data);
            }

            // Debug logging
            error_log('SAP Order: Saved address selection for order ' . $order_id . ': ' . $selected_address_id);
        }
    }
}
add_action('woocommerce_checkout_update_order_meta', 'save_custom_address_selection');

/**
 * Disable WCMCA plugin functionality more aggressively
 */
function disable_wcmca_plugin_functionality() {
    // Remove all WCMCA actions and filters
    global $wp_filter;

    // List of WCMCA hooks to remove
    $wcmca_hooks = array(
        'woocommerce_before_checkout_shipping_form',
        'woocommerce_before_checkout_billing_form',
        'woocommerce_after_checkout_form'
    );

    foreach ($wcmca_hooks as $hook) {
        if (isset($wp_filter[$hook])) {
            foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback_id => $callback) {
                    if (is_array($callback['function']) &&
                        is_object($callback['function'][0]) &&
                        strpos(get_class($callback['function'][0]), 'WCMCA') !== false) {
                        remove_action($hook, $callback['function'], $priority);
                    }
                }
            }
        }
    }

    // Also try to remove by class name patterns
    if (class_exists('WCMCA_CheckoutPage')) {
        $wcmca_checkout = new WCMCA_CheckoutPage();
        remove_action('woocommerce_before_checkout_shipping_form', array($wcmca_checkout, 'add_shipping_address_select_menu'));
        remove_action('woocommerce_before_checkout_billing_form', array($wcmca_checkout, 'add_billing_address_select_menu'));
    }
}
add_action('wp_loaded', 'disable_wcmca_plugin_functionality', 999);

/**
 * Add JavaScript to hide any remaining WCMCA elements
 */
function hide_wcmca_elements_js() {
    if (is_checkout()) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Hide any WCMCA elements that might still appear
            function hideWCMCAElements() {
                $('.wcmca_address_selector_container, .wcmca_address_select_menu, #wcmca_address_select_menu_shipping, #wcmca_address_select_menu_billing').hide();
                $('select[name*="wcmca_"], select[id*="wcmca_"]').hide();
            }

            // Run on page load
            hideWCMCAElements();

            // Run after checkout updates
            $(document.body).on('updated_checkout', function() {
                hideWCMCAElements();
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'hide_wcmca_elements_js');

/**
 * Debug function to check if addresses are being loaded correctly
 */
function debug_custom_addresses() {
    // Debug function disabled for production
    // Uncomment the code below and add ?debug_addresses=1 to URL for debugging
    /*
    if (is_checkout() && current_user_can('administrator') && isset($_GET['debug_addresses'])) {
        $user_id = get_current_user_id();
        $addresses = get_custom_user_addresses($user_id);

        echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border: 1px solid #ccc;">';
        echo '<h3>Debug: Custom Addresses</h3>';
        echo '<p><strong>User ID:</strong> ' . $user_id . '</p>';
        echo '<p><strong>Main User ID:</strong> ' . get_main_b2b_admin_id($user_id) . '</p>';
        echo '<p><strong>Customer ID:</strong> ' . get_user_meta(get_main_b2b_admin_id($user_id), '_customer', true) . '</p>';
        echo '<pre>' . print_r($addresses, true) . '</pre>';
        echo '</div>';
    }
    */
}
add_action('woocommerce_before_checkout_form', 'debug_custom_addresses');

/**
 * Add inline script to debug JavaScript loading
 */
function debug_custom_address_js() {
    // JavaScript debugging disabled for production
    // Uncomment the code below for debugging
    /*
    if (is_checkout()) {
        ?>
        <script type="text/javascript">
        console.log('Custom address debug script loaded');
        console.log('customAddress object:', typeof customAddress !== 'undefined' ? customAddress : 'undefined');
        console.log('ajaxurl:', typeof ajaxurl !== 'undefined' ? ajaxurl : 'undefined');
        console.log('jQuery version:', jQuery.fn.jquery);

        // Test if our custom script loaded
        jQuery(document).ready(function($) {
            console.log('Document ready - checking for custom functions');
            console.log('customOnAddressSelect function:', typeof customOnAddressSelect);
            console.log('customLoadAddressData function:', typeof customLoadAddressData);

            // Check if dropdown exists
            var dropdown = $('.custom_address_select_menu');
            console.log('Custom address dropdown found:', dropdown.length);
            if (dropdown.length) {
                console.log('Dropdown HTML:', dropdown[0].outerHTML);
            }
        });
        </script>
        <?php
    }
    */
}
// Disabled for production
// add_action('wp_footer', 'debug_custom_address_js', 5);

function lf_custom_query_vars($vars)
{
    $vars['view-order'] = 'view-order';
    return $vars;
}
add_filter('woocommerce_get_query_vars', 'lf_custom_query_vars', 0);

function lf_custom_flush_rewrite_rules()
{
    flush_rewrite_rules();
}
add_action('wp_loaded', 'lf_custom_flush_rewrite_rules');

add_filter('woocommerce_my_account_my_orders_actions', function ($actions, $order) {
    $user = wp_get_current_user();
    $allowed_roles = ['b2b_administrator', 'b2b_customer', 'b2b_engineer', 'b2b_viewer'];
    if (array_intersect($allowed_roles, $user->roles)) {
        $actions['view'] = [
            'url' => wc_get_endpoint_url('view-order', $order->get_id()),
            'name' => __('View', 'txtdomain')
        ];
    }
    return $actions;
}, 10, 2);

function lf_view_order_endpoint_content()
{
    $order_id = get_query_var('view-order');
    wc_get_template('myaccount/view-order.php', [
        'order_id' => $order_id,
        'is_customize' => true
    ]);
}
add_action('woocommerce_account_view-order_endpoint', 'lf_view_order_endpoint_content');

function hide_checkout_icon_for_non_b2b_users()
{
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        if (!in_array('b2b_administrator', $user->roles) && !in_array('b2b_customer', $user->roles)) {
            ?>
            <style>
                .widget_shopping_cart_content {
                    display: none !important;
                }

                .single-shop-item.flex.aic {
                    pointer-events: none !important;
                    opacity: 0.5;
                }

                .custom-button {
                    pointer-events: none !important;
                    opacity: 0.5;
                }

                #products-table_info {
                    pointer-events: none !important;
                    opacity: 0.5;
                }
            </style>
            <?php
        }
    }
}
add_action('wp_head', 'hide_checkout_icon_for_non_b2b_users');

function hide_billing_button()
{
    ?>
    <style>
        #wcmca_add_new_address_button_billing {
            display: none !important;
        }
    </style>
    <?php
}
add_filter('woocommerce_account_edit-address_endpoint', 'hide_billing_button', 10, 2);

add_action('wp_ajax_upload_additional_file', 'upload_additional_file');
function upload_additional_file()
{
    $_SESSION['upload_started'] = "true";
    $_SESSION['global_uploaded_file_name'] = "default";
    if (isset($_FILES['additional_files'])) {
        $file = $_FILES['additional_files'];
        $upload = wp_upload_bits($file['name'], null, file_get_contents($file['tmp_name']));

        if (!$upload['error']) {
            $_SESSION['global_uploaded_file_name'] = $upload['url'];
        }
    }
    // Logging disabled for production
    // my_plugin_custom_log("============= Uploaded file name ===============:" . $_SESSION['global_uploaded_file_name']);
    $_SESSION['upload_started'] = "false";
    wp_die(); // Always use wp_die() after an AJAX function to end execution.
}

add_filter('woocommerce_email_attachments', 'attach_csv_to_completed_order_email', 10, 4);

function attach_csv_to_completed_order_email($attachments, $email_id, $order, $email)
{
    if ($email_id === 'new_order' && is_a($order, 'WC_Order') && is_a($email, 'WC_Email_New_Order')) {
        $order_id = $order->get_id(); // Get the order ID
        $csv_file = generate_order_csv($order_id);

        $max_attempts = 100; // Maximum number of polling attempts
        $attempt = 0;
        $upload_status = null;

        // while ($attempt < $max_attempts) {
        //     if (isset($_SESSION['upload_started']) && $_SESSION['upload_started'] == "false") {
        //         break;
        //     }
        //     $attempt++;
        //     sleep(1);
        // }
        // my_plugin_custom_log("============= SESSION STATUS ===============:" . $_SESSION['upload_started']);
        // my_plugin_custom_log("============= attached file name ===============:" . $_SESSION['global_uploaded_file_name']);
        if ($csv_file && file_exists($csv_file)) {
            $attachments[] = $csv_file;
        }
        // if($_SESSION['global_uploaded_file_name'] != "default") {
        //     $attachments[] = $_SESSION['global_uploaded_file_name'];
        //     $_SESSION['global_uploaded_file_name'] = "default";
        // }
        // $additional_file = get_post_meta($order_id, '_additional_files', true);
    }
    return $attachments;
}

function generate_order_csv($order_id)
{
    $order = wc_get_order($order_id);
    if (!$order) {
        error_log("Order not found for ID: $order_id");
        return false;
    }
    $purchase_number = get_post_meta($order_id,"_purchase_order_number", true);
    $upload_dir = wp_upload_dir();
    $file_path = $upload_dir['basedir'] . "/order-report-{$purchase_number}.csv";
    $file = fopen($file_path, 'w');
    if (!$file) {
        error_log("Failed to open file for writing: $file_path");
        return false;
    }
    $header = ['Product Name', 'SKU', 'Quantity', 'Price', 'Total'];
    fputcsv($file, $header);
    foreach ($order->get_items() as $item) {
        $product = $item->get_product();
        $data = [
            $item->get_name(), // Product name
            $product ? $product->get_sku() : '', // SKU
            $item->get_quantity(), // Quantity
            $item->get_total() / $item->get_quantity(), // Price
            $item->get_total() // Total
        ];
        fputcsv($file, $data);
    }
    fclose($file);
    if (!file_exists($file_path)) {
        error_log("File not created successfully: $file_path");
        return false;
    }
    return $file_path;
}

function adding_checkout_addresses($current_addresses_list, $new_addresss_list, $customer_id)
{
    $tmp_new_address = [];
    foreach ($new_addresss_list as $new_address) {
        $tmp_new_address = $new_address;
        if (!isset($new_addresss_list["user_id"])) {
            $tmp_new_address["user_id"] = $customer_id;
        }
        $current_addresses_list[] = $tmp_new_address;
    }
    return $current_addresses_list;
}
function get_sub_user_ids($current_user_id)
{
    $main_user_id = get_main_b2b_admin_id($current_user_id);
    $args = array(
        'meta_key' => '_parent_admin_id',
        'meta_value' => $main_user_id,
        'meta_compare' => '=',
        'fields' => 'ID'
    );
    $user_query = new WP_User_Query($args);
    if (!empty($user_query->get_results())) {
        return $user_query->get_results();
    } else {
        return [];
    }
}

function add_custom_user_meta_column($columns)
{
    if (current_user_can('administrator')) {
        $email_column = $columns['email'];
        $columns['customer_meta'] = __('Customer #', 'woocommerce');
        $columns = array_merge(
            array_slice($columns, 0, array_search('email', array_keys($columns)) + 1),
            ['customer_meta' => __('Customer Meta', 'woocommerce')],
            array_slice($columns, array_search('email', array_keys($columns)) + 1)
        );
    }
    return $columns;
}
add_filter('manage_users_columns', 'add_custom_user_meta_column');

function display_customer_meta_value_in_column($value, $column_name, $user_id)
{
    if (current_user_can('administrator')) {
        if ('customer_meta' === $column_name) {
            $main_user_id = get_main_b2b_admin_id($user_id);
            $customer_meta = get_user_meta($main_user_id, '_customer', true);
            if (empty($customer_meta)) {
                return __('No Customer Meta', 'woocommerce');
            }
            return esc_html($customer_meta);
        }
    }

    return $value;
}
add_filter('manage_users_custom_column', 'display_customer_meta_value_in_column', 10, 3);

function custom_user_search_meta_field($query)
{
    if (is_admin() && isset($query->query_vars['search']) && !empty($query->query_vars['search'])) {
        global $wpdb;
        $search_term = $query->query_vars['search'];
        $search_term = trim($search_term, '*');

        if (!empty($search_term) && preg_match('/^\d+$/', $search_term)) {
            $query->set('meta_query', array(
                array(
                    'key' => '_customer',
                    'value' => $search_term,
                    'compare' => 'LIKE'
                )
            ));
            error_log('Modified query meta_query: ' . print_r($query->get('meta_query'), true));
        }
    }
}
add_action('pre_get_users', 'custom_user_search_meta_field');

add_action(
    'wp_login',
    function () {
        $current_user = wp_get_current_user();
        $main_user_id = get_main_b2b_admin_id($current_user->ID);
        $company_code = get_user_meta($main_user_id, '_companycode', true);
        if ($company_code == "3090") {
            $_SESSION['stock_suffix'] = 'us';
        } else {
            $_SESSION['stock_suffix'] = 'eu';
        }
    },
    10,
    2
);

function get_stock_suffix()
{
    $suffix = "";
    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $company_code = get_user_meta($main_user_id, '_companycode', true);
    if ($company_code == "3090") {
        $suffix = 'us';
    } else {
        $suffix = 'eu';
    }
    return $suffix;
}

add_filter('woocommerce_product_is_in_stock', function ($is_in_stock, $product) {
    error_log("===woocommerce_product_is_in_stock===");
    $suffix = get_stock_suffix();
    $meta_key = '_stock_' . $suffix;
    $stock = (int) get_post_meta($product->get_id(), $meta_key, true);
    return $stock > 0;
}, 10, 2);

// Getting stock quantity and availability based on suffix
add_filter('woocommerce_product_get_stock_quantity', function ($stock_quantity, $product) {
    if (!$product instanceof WC_Product) {
        return $stock_quantity;
    }
    error_log("===woocommerce_product_get_stock_quantity===");
    $suffix = get_stock_suffix();
    $meta_key = '_stock_' . $suffix;
    $custom_stock_quantity = get_post_meta($product->get_id(), $meta_key, true);
    if ($custom_stock_quantity !== '') {
        return intval($custom_stock_quantity);
    } else {
        return 0;
    }
}, 10, 2);

add_filter('woocommerce_get_availability', function ($availability, $product) {
    $suffix = get_stock_suffix();
    if ($suffix !== 'default') {
        $stock_quantity = get_post_meta($product->get_id(), "_stock_{$suffix}", true);
        if ((int) $stock_quantity <= 0) {
            $availability['availability'] = __('Out of stock', 'woocommerce');
            $availability['class'] = 'out-of-stock';
        }
    }

    return $availability;
}, 10, 2);

// Valide stock during add to cart
add_filter('woocommerce_add_to_cart_validation', 'custom_stock_us_validation', 10, 5);
function custom_stock_us_validation($passed, $product_id, $quantity, $variation_id = 0, $cart_item_data = [])
{
    // Logging disabled for production
    // my_plugin_custom_log("passed: $passed");
    $custom_suffix = get_stock_suffix();
    $manage_stock = get_post_meta($product_id, '_manage_stock_' . $custom_suffix, true);
    $stock_quantity = (int) get_post_meta($product_id, '_stock_' . $custom_suffix, true);
    $backorders = get_post_meta($product_id, '_backorders_' . $custom_suffix, true);

    // Logging disabled for production
    // my_plugin_custom_log("== ADD TO CAT VAL == manage stock : $manage_stock, stock quantity : $stock_quantity, Back-orders: $backorders, Requested Quantity: $quantity");
    if ($manage_stock === 'yes') {
        // If the product is out of stock and backorders are not allowed
        if ($stock_quantity < $quantity && $backorders !== 'yes') {
            wc_add_notice(__('Sorry, this product is not available in the requested quantity.', 'woocommerce'), 'error');
            return false;
        }
        else{
            return true;
        }
    }
    return $passed;

}

// add_action('woocommerce_checkout_process', function () {
//     $suffix = get_stock_suffix();
//     $cart = WC()->cart->get_cart();

//     foreach ($cart as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $quantity = $cart_item['quantity'];
//         $stock_quantity = (int) get_post_meta($product_id, "_stock_" . $suffix, true);

//         if ($quantity > $stock_quantity) {
//             wc_add_notice(
//                 sprintf(
//                     __('Not enough stock for "%s" in your region. Available: %d.', 'woocommerce'),
//                     $cart_item['data']->get_name(),
//                     $stock_quantity
//                 ),
//                 'error'
//             );
//         }
//     }
// });

add_filter('woocommerce_prevent_admin_stock_reduction', function ($prevent, $order) {
    $suffix = get_stock_suffix();
    $meta_field = "_stock_" . $suffix;
    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        $custom_stock = get_post_meta($product_id, $meta_field, true);
        $quantity = $item->get_quantity();

        if ($quantity > $custom_stock) {
            return true;
        } else {
            false;
        }
    }
}, 10, 2);

add_filter('woocommerce_cart_product_cannot_be_purchased_message', function ($message, $product_id) {
    return ''; // Return an empty message to bypass stock errors
}, 10, 2);

// Display available stock in cart and checkout region-based
add_filter('woocommerce_get_item_data', function ($item_data, $cart_item) {
    $suffix = get_stock_suffix();
    if ($suffix !== 'default') {
        $product_id = $cart_item['product_id'];
        $stock_quantity = get_post_meta($product_id, "_stock_{$suffix}", true);
        $item_data[] = array(
            'key' => __('Available Stock', 'woocommerce'),
            'value' => $stock_quantity,
        );
    }

    return $item_data;
}, 10, 2);

// Inject SAP Net Price and Sales Tax per cart line (TEMPORARILY DISABLED)
/*add_filter('woocommerce_get_item_data', function ($item_data, $cart_item) {
    try {
        if ( ! is_user_logged_in() ) {
            return $item_data;
        }
        $product = isset($cart_item['data']) ? $cart_item['data'] : null;
        if ( ! $product || ! ($product instanceof WC_Product) ) {
            return $item_data;
        }
        if ( ! function_exists('hytec_build_sales_order_payload') || ! function_exists('hytec_call_sap_sales_order') ) {
            if ( function_exists('hytec_sap_console_log') ) {
                hytec_sap_console_log('warn', 'SAP cart pricing: required functions missing');
            }
            return $item_data;
        }

        $user_id = get_current_user_id();
        $qty     = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

        if ( function_exists('hytec_sap_console_log') ) {
            hytec_sap_console_log('info', 'SAP cart pricing: building payload', array(
                'product_id' => $product->get_id(),
                'sku'        => $product->get_sku(),
                'qty'        => $qty,
                'user_id'    => $user_id,
            ));
        }

        // Simple per-request cache to avoid duplicate calls for same product+qty
        static $cache = array();
        $cache_key = $product->get_id() . '|' . $qty;
        $net_value = '';
        $sales_tax = '';

        if ( isset( $cache[$cache_key] ) ) {
            $net_value = $cache[$cache_key]['net_value'];
            $sales_tax = $cache[$cache_key]['sales_tax'];
        } else {
            $payload  = hytec_build_sales_order_payload( $product, $user_id, $qty );
            if ( ! $payload ) {
                if ( function_exists('hytec_sap_console_log') ) {
                    hytec_sap_console_log('warn', 'SAP cart pricing: no payload (likely missing Sold-To)');
                }
                return $item_data;
            }
            $response = hytec_call_sap_sales_order( $payload );
            if ( is_wp_error( $response ) ) {
                if ( function_exists('hytec_sap_console_log') ) {
                    hytec_sap_console_log('error', 'SAP cart pricing: call failed', array(
                        'code' => $response->get_error_code(),
                        'msg'  => $response->get_error_message(),
                    ));
                }
                return $item_data; // fail silent on cart for UX
            }

            // store a copy for debug UI in footer
            if ( ! isset($GLOBALS['HYTEC_SAP_CART_DEBUG']) || ! is_array($GLOBALS['HYTEC_SAP_CART_DEBUG']) ) {
                $GLOBALS['HYTEC_SAP_CART_DEBUG'] = array();
            }
            $GLOBALS['HYTEC_SAP_CART_DEBUG'][] = array(
                'product_id' => $product->get_id(),
                'sku'        => $product->get_sku(),
                'qty'        => $qty,
                'response'   => $response,
            );

            $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
            $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
            $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
            $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
            $cache[$cache_key] = array('net_value'=>$net_value, 'sales_tax'=>$sales_tax);

            if ( function_exists('hytec_sap_console_log') ) {
                hytec_sap_console_log('info', 'SAP cart pricing: response OK', array(
                    'net_value' => $net_value,
                    'sales_tax' => $sales_tax,
                    'raw_first' => $first,
                ));
            }
        }

        if ( $net_value !== '' ) {
            // Convert SAP response (divide by 100) and format with currency symbol
            $net_value_decimal = is_numeric( $net_value ) ? (float)$net_value / 100 : 0;
            $formatted_net_price = is_numeric( $net_value ) ? get_woocommerce_currency_symbol() . number_format( $net_value_decimal, 2, '.', '' ) : wc_clean( (string) $net_value );
            $item_data[] = array(
                'key'   => __('Net Price', 'woocommerce'),
                'value' => wc_clean( (string) $net_value_decimal ),
                'display' => $formatted_net_price,
            );
        }
        if ( $sales_tax !== '' ) {
            // Convert SAP response (divide by 100) and format with currency symbol
            $sales_tax_decimal = is_numeric( $sales_tax ) ? (float)$sales_tax / 100 : 0;
            $formatted_sales_tax = is_numeric( $sales_tax ) ? get_woocommerce_currency_symbol() . number_format( $sales_tax_decimal, 2, '.', '' ) : wc_clean( (string) $sales_tax );
            $item_data[] = array(
                'key'   => __('Sales Tax', 'woocommerce'),
                'value' => wc_clean( (string) $sales_tax_decimal ),
                'display' => $formatted_sales_tax,
            );
        }

        // Optional: small hint for admins
        if ( function_exists('hytec_sap_debug_ui_enabled') && hytec_sap_debug_ui_enabled() && current_user_can('manage_options') ) {
            $item_data[] = array(
                'key'   => __('SAP Debug', 'woocommerce'),
                'value' => __('See browser console for SAP pricing logs', 'woocommerce'),
            );
        }

// Render SAP cart debug UI similar to product page
add_action('wp_footer', function() {
    if ( ! function_exists('is_cart') || ! is_cart() ) return;
    if ( ! function_exists('hytec_sap_debug_ui_enabled') || ! hytec_sap_debug_ui_enabled() ) return;

    // Only for logged-in users to avoid leaking details
    if ( ! is_user_logged_in() ) return;

    echo '<div class="sap-cart-debug" style="margin:12px 0;">';

    // Show what items are in cart and their quantities
    if ( function_exists('WC') && WC()->cart ) {
        echo '<details class="sap-cart-items"><summary>Cart items (debug)</summary><div style="font-family:monospace;font-size:12px;">';
        foreach ( WC()->cart->get_cart() as $cart_item_key => $ci ) {
            $p = isset($ci['data']) ? $ci['data'] : null;
            if ( $p instanceof WC_Product ) {
                echo '<div>Product #' . esc_html( (string) $p->get_id() ) . ' SKU ' . esc_html( $p->get_sku() ) . ' — Qty ' . esc_html( (string) intval($ci['quantity']) ) . '</div>';
            }
        }
        echo '</div></details>';
    }

    // Print last request snapshot (sanitized) if available (from last SAP call)
    if ( function_exists('hytec_sap_print_request_debug') ) {
        hytec_sap_print_request_debug();
    }

    // Print captured responses for this cart render
    if ( isset($GLOBALS['HYTEC_SAP_CART_DEBUG']) && is_array($GLOBALS['HYTEC_SAP_CART_DEBUG']) && ! empty($GLOBALS['HYTEC_SAP_CART_DEBUG']) ) {
        foreach ( $GLOBALS['HYTEC_SAP_CART_DEBUG'] as $k => $entry ) {
            $pid = isset($entry['product_id']) ? intval($entry['product_id']) : 0;
            $sku = isset($entry['sku']) ? (string) $entry['sku'] : '';
            $qty = isset($entry['qty']) ? intval($entry['qty']) : 0;
            $res = isset($entry['response']) ? $entry['response'] : array();
            echo '<details class="sap-response-debug" style="margin-top:8px;">';
            echo '<summary>SAP API Response (Cart debug) — Product #' . esc_html( (string) $pid ) . ' SKU ' . esc_html( $sku ) . ' Qty ' . esc_html( (string) $qty ) . '</summary>';
            echo '<pre style="white-space:pre-wrap;max-height:400px;overflow:auto;">' . esc_html( print_r( $res, true ) ) . '</pre>';
            echo '</details>';
        }
    } else {
        echo '<div style="color:#888;font-size:12px;">No SAP cart responses captured in this render.</div>';
    }

    echo '</div>';
});

    } catch ( \Throwable $e ) {
        if ( function_exists('hytec_sap_console_log') ) {
            hytec_sap_console_log('error', 'SAP cart pricing exception', array('msg'=>$e->getMessage()));
        }
        // swallow errors in cart UI
    }
    return $item_data;
}, 20, 2);

// Alternative approach: Hook directly into cart item display
add_action('woocommerce_after_cart_item_name', function($cart_item, $cart_item_key) {
    try {
        if ( ! is_user_logged_in() ) {
            return;
        }

        $product = isset($cart_item['data']) ? $cart_item['data'] : null;
        if ( ! $product || ! ($product instanceof WC_Product) ) {
            return;
        }

        if ( ! function_exists('hytec_build_sales_order_payload') || ! function_exists('hytec_call_sap_sales_order') ) {
            return;
        }

        $user_id = get_current_user_id();
        $qty     = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

        // Simple per-request cache to avoid duplicate calls for same product+qty
        static $cache = array();
        $cache_key = $product->get_id() . '|' . $qty;
        $net_value = '';
        $sales_tax = '';

        if ( isset( $cache[$cache_key] ) ) {
            $net_value = $cache[$cache_key]['net_value'];
            $sales_tax = $cache[$cache_key]['sales_tax'];
        } else {
            $payload  = hytec_build_sales_order_payload( $product, $user_id, $qty );
            if ( ! $payload ) {
                return;
            }
            $response = hytec_call_sap_sales_order( $payload );
            if ( is_wp_error( $response ) ) {
                return;
            }

            $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
            $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
            $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
            $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
            $cache[$cache_key] = array('net_value'=>$net_value, 'sales_tax'=>$sales_tax);
        }

        // Display SAP pricing if we have data
        /*if ( $net_value !== '' || $sales_tax !== '' ) {
            echo '<div class="sap-cart-pricing" style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007cba;">';
            echo '<div class="sap-pricing-title" style="font-weight: 600; font-size: 12px; color: #555; margin-bottom: 4px;">SAP Pricing</div>';
            echo '<div class="sap-pricing-values" style="display: flex; gap: 15px; font-size: 13px;">';

            if ( $net_value !== '' ) {
                $formatted_net_price = is_numeric( $net_value ) ? wc_price( $net_value ) : esc_html( $net_value );
                echo '<div class="sap-pricing-item">';
                echo '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Net Price:</span>';
                echo '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' . $formatted_net_price . '</span>';
                echo '</div>';
            }

            if ( $sales_tax !== '' ) {
                $formatted_sales_tax = is_numeric( $sales_tax ) ? wc_price( $sales_tax ) : esc_html( $sales_tax );
                echo '<div class="sap-pricing-item">';
                echo '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Sales Tax:</span>';
                echo '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' . $formatted_sales_tax . '</span>';
                echo '</div>';
            }

            echo '</div>';
            echo '</div>';
        }

    } catch ( \Throwable $e ) {
        // Silently handle errors in cart display
    }
}, 10, 2);*/



add_action('woocommerce_checkout_create_order', 'update_custom_stock_on_order', 10, 2);

function update_custom_stock_on_order($order, $data)
{
    $suffix = get_stock_suffix();
    $order_id = $order->get_id();
    $items = $order->get_items();
    $order->set_currency(get_custom_currency());
    foreach ($items as $item) {
        $product_id = $item->get_product_id();
        $quantity = $item->get_quantity();
        $current_stock = (int) get_post_meta($product_id, '_stock_' . $suffix, true);
        if ($current_stock !== '') {
            // $new_stock = max(0, $current_stock - (int) $quantity); // Prevent negative stock
            $new_stock =  (int)$current_stock - (int) $quantity; // Prevent negative stock
            update_post_meta($product_id, '_stock_' . $suffix, $new_stock);
            // Logging disabled for production
            // my_plugin_custom_log(get_post_meta($product_id, '_stock_' . $suffix, true));
        }
    }
}

// Change woocommerce_currency
add_filter('woocommerce_currency', function ($currency) {
    $custom_currency = get_custom_currency();
    return $custom_currency;
});

// add_action('woocommerce_check_cart_items', function () {
//     $suffix = get_stock_suffix();
//     $meta_stock_key = "_stock_" . $suffix;

//     foreach (WC()->cart->get_cart() as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $quantity = $cart_item['quantity'];
//         $available_stock = (int) get_post_meta($product_id, $meta_stock_key, true);

//         if ($available_stock < $quantity) {
//             wc_add_notice(
//                 sprintf(
//                     'Not enough units of %s are available in stock to fulfill this order.',
//                     get_the_title($product_id)
//                 ),
//                 'error'
//             );
//         }
//     }
// });

// add_action('woocommerce_after_checkout_validation', function ($data, $errors) {
//     if (!empty($errors->get_error_messages())) {
//         return;
//     }
//     $suffix = get_stock_suffix();
//     $meta_stock_key = "_stock_" . $suffix;
//     foreach (WC()->cart->get_cart() as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $custom_stock = get_post_meta($product_id, $meta_stock_key, true); // Custom stock field
//         $quantity = $cart_item['quantity'];

//         if ($quantity > $custom_stock) {
//             $errors->add('stock_validation_error', __('Custom stock validation failed.', 'woocommerce'));
//         }
//     }
// }, 10, 2);


add_filter('woocommerce_get_availability_text', function ($availability, $product) {
    $suffix = get_stock_suffix();
    $meta_stock_key = '_stock_' . $suffix;
    $stock = (int) get_post_meta($product->get_id(), $meta_stock_key, true);
    if ($stock > 0) {
        return sprintf(__('In stock (%d units)', 'woocommerce'), $stock);
    } else {
        return __('Out of stock', 'woocommerce');
    }
}, 10, 2);

// add_action('woocommerce_checkout_process', function () {
//     foreach (WC()->cart->get_cart() as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $quantity = $cart_item['quantity'];
//         $suffix = get_stock_suffix();
//         $meta_stock_key = '_stock_' . $suffix;
//         $available_stock = (int) get_post_meta($product_id, $meta_stock_key, true);
//         if ($available_stock < $quantity) {
//             wc_add_notice(
//                 sprintf(
//                     'Not enough units of %s are available in stock to fulfill this order.',
//                     "woocommerce_checkout_process"
//                 ),
//                 'error'
//             );
//         }
//     }
// });

add_filter('woocommerce_product_get_stock_quantity', function ($stock, $product) {
    $suffix = get_stock_suffix(); // Your logic to determine the region (US/EU).
    $custom_stock_meta = '_stock_' . $suffix;
    // Fetch custom stock value
    $custom_stock = get_post_meta($product->get_id(), $custom_stock_meta, true);

    if ($custom_stock !== '') {
        return (int) $custom_stock; // Return custom stock.
    }
    return (int) $custom_stock; // Default fallback.
}, 10, 2);

// add_filter('woocommerce_product_is_in_stock', function ($is_in_stock, $product) {
//     $suffix = get_stock_suffix();
//     $custom_stock_meta = '_stock_' . $suffix;
//     $custom_stock = get_post_meta($product->get_id(), $custom_stock_meta, true);
//     if ($custom_stock <= 0) {
//         return false;
//     }
//     return true;
// }, 10, 2);

add_filter('manage_edit-shop_order_columns', 'add_custom_order_column');
function add_custom_order_column($columns)
{
    if (isset($columns['order_number'])) {
        unset($columns['order_number']);
    }
    if (isset($columns['order_total'])) {
        unset($columns['order_total']);
    }
    $new_column = array('custom_order_number' => __('Order', 'woocommerce'));
    $first_column = array_slice($columns, 0, 1, true);
    $rest_columns = array_slice($columns, 1, null, true);
    $columns = array_merge($first_column, $new_column, $rest_columns);
    $columns['custom_total'] = __('Total', 'woocommerce');
    return $columns;
}

// Step 2: Populate the custom column with your custom total
add_action('manage_shop_order_posts_custom_column', 'populate_custom_order_column', 10, 2);
function populate_custom_order_column($column, $post_id)
{
    $order = wc_get_order($post_id);
    if ($column === 'custom_order_number') {
        $purchase_order_id = get_post_meta($post_id, '_purchase_order_number', true);
        $customer_id = $order->get_customer_id();
        $customer_name = "";
        if ($customer_id) {
            $customer_user = get_userdata($customer_id);
            if ($customer_user) {
                $customer_name = ucfirst($customer_user->user_login);
            }
        }

        echo '<a href="#" class="order-preview" data-order-id="' . $post_id . '" title="Preview">Preview</a><a href="/wp-admin/post.php?post=' . $post_id . '&amp;action=edit" class="order-view"><strong>#' . $purchase_order_id . ' ' . $customer_name . '</strong></a>';
    }

    if ($column === 'custom_total') {
        $currency = get_post_meta($post_id, '_order_currency', true);
        if (!$currency) {
            $currency = $order->get_currency();
        }
        if ($currency == "USD") {
            $currency_symbol = "$";
        } else if ($currency == "EUR") {
            $currency_symbol = "€";
        } else {
            $currency_symbol = "£";
        }
        $order_total = $order->get_total();
        echo $currency_symbol . ' ' . $order_total;
    }
}

add_filter('manage_edit-shop_order_sortable_columns', function ($sortable_columns) {
    $sortable_columns['custom_order_number'] = '_purchase_order_number';
    return $sortable_columns;
});

// Handle sorting query
add_action('pre_get_posts', function ($query) {
    if (!is_admin())
        return;

    $orderby = $query->get('orderby');
    if ('custom_order_number' === $orderby) {
        $query->set('meta_key', '_purchase_order_number');
        $query->set('orderby', 'meta_value_num');
    }
});

add_action('woocommerce_admin_order_data_after_order_details', 'set_custom_currency_script');
function set_custom_currency_script($order)
{
    $order_id = $order->get_id();
    $order_currency = get_post_meta($order_id, '_order_currency', true);
    if (!$order_currency) {
        $order_currency = $order->get_currency();
    }

    $currency_symbol = "";
    if ($order_currency == "USD") {
        $currency_symbol = "$";
    } else if ($order_currency == "EUR") {
        $currency_symbol = "€";
    } else {
        $currency_symbol = "£";
    }

    $purchase_order_number = get_post_meta($order_id, '_purchase_order_number', true);
    $purchase_order_number = "Order #" . $purchase_order_number . " details";
    ?>
    <script>
        jQuery(document).ready(function ($) {
            $('.woocommerce-Price-currencySymbol').text('<?php echo esc_js($currency_symbol); ?>');
            $('.woocommerce-order-data__heading').text('<?php echo esc_js($purchase_order_number); ?>');
        });
    </script>
    <?php
}


add_action('woocommerce_email_header', 'custom_email_header_content', 10, 2);
function custom_email_header_content($email_heading, $email)
{
    // Check if the email object exists and contains an order
    if (isset($email->object) && is_a($email->object, 'WC_Order')) {
        $order = $email->object; // Get the order object
        $p_n = get_post_meta($order->get_id(), '_purchase_order_number', true);
        $order_date = wc_format_datetime($order->get_date_created());
        ?>
        <body <?php echo is_rtl() ? 'rightmargin' : 'leftmargin'; ?>="0" marginwidth="0" topmargin="0" marginheight="0"
            offset="0">
            <table width="100%" id="outer_wrapper">
                <tr>
                    <td><!-- Deliberately empty to support consistent sizing and layout across multiple email clients. --></td>
                    <td width="600">
                        <div id="wrapper" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
                            <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%">
                                <tr>
                                    <td align="center" valign="top">
                                        <div id="template_header_image">
                                            <?php
                                            $img = get_option('woocommerce_email_header_image');

                                            if ($img) {
                                                echo '<p style="margin-top:0;"><img src="' . esc_url($img) . '" alt="' . esc_attr(get_bloginfo('name', 'display')) . '" /></p>';
                                            }
                                            ?>
                                        </div>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" id="template_container">
                                            <tr>
                                                <td align="center" valign="top">
                                                    <!-- Header -->
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        id="template_header">
                                                        <tr>
                                                            <td id="header_wrapper">
                                                                <h1><?php echo "New Order: #" . $p_n; ?></h1>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <!-- End Header -->
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="center" valign="top">
                                                    <!-- Body -->
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        id="template_body">
                                                        <tr>
                                                            <td valign="top" id="body_content">
                                                                <!-- Content -->
                                                                <table border="0" cellpadding="20" cellspacing="0" width="100%">
                                                                    <tr>
                                                                        <td valign="top">
                                                                            <div id="body_content_inner"></div>
                                                                            <?php
    }
}

add_filter( 'woocommerce_email_subject_new_order', 'custom_new_order_email_subject', 10, 2 );
function custom_new_order_email_subject( $subject, $order ) {
    $p_n = get_post_meta($order->get_id(), '_purchase_order_number', true);
    $subject = sprintf( 'New Order #%s', $p_n );

    return $subject;
}

add_filter('woocommerce_email_recipient_new_order', 'route_order_emails_by_country', 10, 2);
function route_order_emails_by_country($recipient, $order) {
    if (!($order instanceof WC_Order)) {
        return $recipient;
    }

    $customer_id = $order->get_customer_id();
    $main_admin_id = get_main_b2b_admin_id($customer_id);

    $company_code = get_user_meta($main_admin_id, '_companycode', true);

    $us_email = '<EMAIL>';
    // $eu_gb_email = '<EMAIL>';
    $eu_gb_email = '<EMAIL>';

    // $us_email = '<EMAIL>';
    // $eu_gb_email = '<EMAIL>';

    my_plugin_custom_log($company_code);
    if ($company_code === '3090') {
        $recipient = $us_email;
    } else{
        $recipient = $eu_gb_email;
    }

    return $recipient;
}

function my_plugin_custom_log($message) {
    $log_file = plugin_dir_path(__FILE__) . 'my-plugin-log.txt'; // Specify file location
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}


add_filter('auth_cookie_expiration', 'custom_session_expiration_time', 99, 3);
function custom_session_expiration_time($expiration, $user_id, $remember) {
    return 86400;
}

// add_action('wp_login', 'check_password_expiration', 10, 2);

// function check_password_expiration($user_login, $user) {
//     $password_changed_date = get_user_meta($user->ID, 'password_changed_date', true);
//     if (!$password_changed_date) {
//         $password_changed_date = current_time('timestamp');
//         update_user_meta($user->ID, 'password_changed_date', $password_changed_date);
//     }

//     $days_since_change = (current_time('timestamp') - $password_changed_date) / DAY_IN_SECONDS;

//     if ($days_since_change > 90) {
//         wp_logout();
//         wp_die('Your password has expired. Please reset your password to continue.');
//     }
// }

add_action('woocommerce_add_to_cart', 'debug_add_to_cart', 10, 6);

function debug_add_to_cart($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data)
{
    // Logging disabled for production
    // my_plugin_custom_log("Add to cart debug: Product ID: $product_id, Quantity: $quantity");
}

add_filter('woocommerce_add_cart_item_data', 'custom_add_cart_item_data', 10, 2);

function custom_add_cart_item_data($cart_item_data, $product_id) {
    $custom_suffix = get_stock_suffix();
    $manage_stock = get_post_meta($product_id, '_manage_stock_' . $custom_suffix, true);
    $stock_quantity = (int) get_post_meta($product_id, '_stock_' . $custom_suffix, true);
    $backorders = get_post_meta($product_id, '_backorders_' . $custom_suffix, true);
    if ($manage_stock === 'yes') {
        // Custom backorder logic
        if ($stock_quantity <= 0 && $backorders === 'yes') {
            $cart_item_data['is_backorder'] = true; // Custom marker for backorder items
        } elseif ($stock_quantity < $cart_item_data['quantity']) {
            return false;
        }
    }
    return $cart_item_data;
}
add_filter('woocommerce_cart_item_is_backorder', 'custom_cart_item_backorder', 10, 2);

function custom_cart_item_backorder($is_backorder, $cart_item) {
    if (isset($cart_item['is_backorder']) && $cart_item['is_backorder'] === true) {
        return true;
    }
    return $is_backorder;
}
add_action('admin_init', function () {
    remove_action('admin_color_scheme_picker', 'admin_color_scheme_picker');
});
add_action('admin_head', function () {
    // Hide the fields with CSS
    echo '<style>
        tr.user-rich-editing-wrap,
        tr.user-comment-shortcuts-wrap,
        tr.show-admin-bar,
        tr.user-language-wrap {
            display: none !important;
        }
		#tax_exempt_options,
        .wc-customer-tax-exempt,
        .woocommerce-tax-exempt-wrap {
            display: none !important;
        }
    </style>';
});

// Optional: remove the language dropdown server-side
add_filter('get_user_option_locale', function($locale, $user_id) {
    return get_locale(); // Forces the site locale
}, 10, 2);
add_action('admin_init', function () {
    // Only run on user-new.php and when redirected after creating a user
    if (is_admin() && isset($_GET['update']) && $_GET['update'] === 'add' && current_user_can('edit_users')) {
        // Get the latest user created by ID (may be rough, but WordPress doesn’t pass ID in URL)
        $recent_user = get_users([
            'number' => 1,
            'orderby' => 'ID',
            'order' => 'DESC',
            'fields' => ['ID']
        ]);

        if (!empty($recent_user)) {
            $user_id = $recent_user[0]->ID;
            wp_redirect(admin_url("user-edit.php?user_id={$user_id}"));
            exit;
        }
    }
});

// remove_filter( 'wp_authenticate_user', 'wp_check_password_reset_key_expired' );

// Billing and shipping addresses fields
add_filter( 'woocommerce_default_address_fields' , 'filter_default_address_fields', 20, 1 );
function filter_default_address_fields( $address_fields ) {
    // Only on checkout page
    if( ! is_checkout() ) return $address_fields;

    // All field keys in this array
    $key_fields = array('company');

    // Loop through each address fields (billing and shipping)
    foreach( $key_fields as $key_field )
        $address_fields[$key_field]['required'] = false;

    return $address_fields;
}
// Removed: Phone field function - not needed since we don't have shipping phone field
// add_filter( 'woocommerce_checkout_fields', 'your_require_wc_phone_field');
// function your_require_wc_phone_field( $fields ) {
//     if (isset($fields['shipping']['shipping_phone'])) {
//         $fields['shipping']['shipping_phone']['required'] = false;
//     }
//     return $fields;
// }



add_filter( 'woocommerce_checkout_fields' , 'custom_remove_billing_phone_field' );

function custom_remove_billing_phone_field( $fields ) {
    unset($fields['billing']['billing_phone']);
    return $fields;
}

// Add Export Compliance checkbox based on selected shipping country
add_action('woocommerce_review_order_before_submit', 'add_export_compliance_checkbox');
function add_export_compliance_checkbox() {
    ?>
    <div id="export_compliance_container" class="form-row terms wc-terms-and-conditions" style="margin-bottom:1em; display:none;text-align: left;">
        <p class="woocommerce-privacy-policy-text" style="margin-bottom: 1em;text-align: left;">
            Purchasing party shall comply with all export laws and regulations of the United States and other applicable jurisdictions.
            Purchasing party represents that it is not named on any U.S. government list of persons or entities prohibited from receiving exports,
            and you shall not permit products to be purchased in violation of any U.S. export embargo, prohibition or restriction.
        </p>
        <p class="custom-check form-row validate-required" style="margin-bottom:0;">
            <input type="checkbox" id="export_compliance_input" class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" id="export_compliance" name="export_compliance" />
            <label for="export_compliance_input" class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
                <span class="woocommerce-form__label-text">I agree with the Export Compliance</span>&nbsp;<abbr class="required" title="required">*</abbr>
            </label>
        </p>
    </div>
    <script>
    jQuery(function($){
        function toggleExportComplianceBox(){
            var country = $('#shipping_country').val() || '';
            var isUS = (country.toUpperCase() === 'US');
            var $box = $('#export_compliance_container');
            if (isUS) {
                $box.hide();
                $('#export_compliance').prop('checked', false);
            } else {
                $box.show();
            }
        }
        // Initial check and on updates
        toggleExportComplianceBox();
        $(document.body).on('change', '#shipping_country', toggleExportComplianceBox);
        $(document.body).on('country_to_state_changed updated_checkout', toggleExportComplianceBox);
    });
    </script>
    <?php
}

// Save export compliance checkbox value
add_action('woocommerce_checkout_update_order_meta', 'save_export_compliance_checkbox');
function save_export_compliance_checkbox($order_id) {
    $shipping_country = isset($_POST['shipping_country']) ? wc_clean( wp_unslash( $_POST['shipping_country'] ) ) : '';
    $is_us_shipping = ( strtoupper( (string) $shipping_country ) === 'US' );

    // Only process for non-US shipping destinations
    if ( ! $is_us_shipping ) {
        if ( isset($_POST['export_compliance']) && $_POST['export_compliance'] ) {
            update_post_meta($order_id, '_export_compliance_agreed', 'yes');
            update_post_meta($order_id, '_export_compliance_date', current_time('mysql'));
        }
    } else {
        // Ensure not set for US shipments
        delete_post_meta($order_id, '_export_compliance_agreed');
        delete_post_meta($order_id, '_export_compliance_date');
    }
}

// Validate export compliance on checkout
add_action('woocommerce_checkout_process', 'validate_export_compliance_checkbox');
function validate_export_compliance_checkbox() {
    $shipping_country = isset($_POST['shipping_country']) ? wc_clean( wp_unslash( $_POST['shipping_country'] ) ) : '';
    $is_us_shipping = ( strtoupper( (string) $shipping_country ) === 'US' );

    // Only validate for non-US shipping destinations
    if ( ! $is_us_shipping ) {
        if ( ! isset($_POST['export_compliance']) || ! $_POST['export_compliance'] ) {
            wc_add_notice('Please read and accept the Export Compliance agreement to proceed.', 'error');
        }
    }
}

// Validate Shipping Method Preference and Shipping Freight Terms dropdowns
add_action('woocommerce_checkout_process', 'validate_shipping_dropdown_fields');
function validate_shipping_dropdown_fields() {
    // Validate Shipping Method Preference
    if (!isset($_POST['shipping_method_preference']) || empty($_POST['shipping_method_preference'])) {
        wc_add_notice('Shipping Method Preference is a required field.', 'error');
    }

    // Validate Shipping Freight Terms (shipping_account_number field)
    if (!isset($_POST['shipping_account_number']) || empty($_POST['shipping_account_number'])) {
        wc_add_notice('Shipping Freight Terms is a required field.', 'error');
    }
}

// Add SAP pricing to cart page via JavaScript injection for WooCommerce Blocks
add_action('wp_footer', function() {
    if ( ! is_cart() || ! is_user_logged_in() ) {
        return;
    }

    // Get SAP pricing data for all cart items
    $sap_pricing_data = array();

    if ( WC()->cart && ! WC()->cart->is_empty() ) {
        foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
            $product = isset($cart_item['data']) ? $cart_item['data'] : null;
            if ( ! $product || ! ($product instanceof WC_Product) ) {
                continue;
            }

            if ( ! function_exists('hytec_build_sales_order_payload') || ! function_exists('hytec_call_sap_sales_order') ) {
                continue;
            }

            $user_id = get_current_user_id();
            $qty     = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

            // Simple per-request cache to avoid duplicate calls for same product+qty
            static $cache = array();
            $cache_key = $product->get_id() . '|' . $qty;
            $net_value = '';
            $sales_tax = '';

            if ( isset( $cache[$cache_key] ) ) {
                $net_value = $cache[$cache_key]['net_value'];
                $sales_tax = $cache[$cache_key]['sales_tax'];
            } else {
                $payload  = hytec_build_sales_order_payload( $product, $user_id, $qty );
                if ( ! $payload ) {
                    continue;
                }
                $response = hytec_call_sap_sales_order( $payload );
                if ( is_wp_error( $response ) ) {
                    continue;
                }

                $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
                $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
                $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
                $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
                $cache[$cache_key] = array('net_value'=>$net_value, 'sales_tax'=>$sales_tax);
            }

            // Add SAP pricing to data array
            if ( $net_value !== '' || $sales_tax !== '' ) {
                $sap_pricing_data[$product->get_sku()] = array(
                    'net_value' => $net_value,
                    'sales_tax' => $sales_tax,
                    'formatted_net_value' => is_numeric( $net_value ) ? wc_price( (float)$net_value / 100 ) : $net_value,
                    'formatted_sales_tax' => is_numeric( $sales_tax ) ? wc_price( (float)$sales_tax / 100 ) : $sales_tax,
                );
            }
        }
    }

    if ( ! empty( $sap_pricing_data ) ) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var sapPricingData = <?php echo wp_json_encode( $sap_pricing_data ); ?>;

            function addSapPricing() {
                // Find all actual cart items (not totals or other price elements)
                $('.wc-block-cart-item').each(function(index) {
                    var $cartItem = $(this);

                    // Skip if SAP pricing already added to this cart item
                    if ($cartItem.find('.sap-cart-pricing').length > 0) {
                        return;
                    }

                    // Find the price element within this specific cart item
                    var $priceElement = $cartItem.find('.wc-block-formatted-money-amount.wc-block-components-formatted-money-amount.wc-block-components-product-price__value').first();

                    if ($priceElement.length === 0) {
                        return; // No price element found in this cart item
                    }

                    // Try to find product SKU from the cart item
                    var productSku = null;

                    // Method 1: Look for SKU in the product details text
                    var $cartItemText = $cartItem.text();

                    for (var sku in sapPricingData) {
                        if ($cartItemText.indexOf(sku) !== -1) {
                            productSku = sku;
                            break;
                        }
                    }

                    // Method 2: If no SKU found, try to match by index (fallback)
                    if (!productSku) {
                        var skuKeys = Object.keys(sapPricingData);
                        if (skuKeys[index]) {
                            productSku = skuKeys[index];
                        }
                    }

                    // If we found a matching SKU, add the SAP pricing
                    if (productSku && sapPricingData[productSku]) {
                        var sapData = sapPricingData[productSku];

                        var sapHtml = '<div class="sap-cart-pricing" style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007cba;">' +
                            '<div class="sap-pricing-title" style="font-weight: 600; font-size: 12px; color: #555; margin-bottom: 4px;">SAP Pricing</div>' +
                            '<div class="sap-pricing-values" style="display: flex; gap: 15px; font-size: 13px;">';

                        if (sapData.formatted_net_value) {
                            sapHtml += '<div class="sap-pricing-item">' +
                                '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Net Price:</span>' +
                                '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' + sapData.formatted_net_value + '</span>' +
                                '</div>';
                        }

                        if (sapData.formatted_sales_tax) {
                            sapHtml += '<div class="sap-pricing-item">' +
                                '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Sales Tax:</span>' +
                                '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' + sapData.formatted_sales_tax + '</span>' +
                                '</div>';
                        }

                        sapHtml += '</div></div>';

                        // Insert SAP pricing after the price element
                        $priceElement.after(sapHtml);
                    }
                });
            }

            // Add SAP pricing on page load
            addSapPricing();

            // Re-add SAP pricing when cart updates (for AJAX updates)
            $(document.body).on('updated_cart_totals updated_checkout', function() {
                setTimeout(addSapPricing, 500);
            });

            // Also try with a slight delay for blocks that load asynchronously
            setTimeout(addSapPricing, 1000);
        });
        </script>
        <?php
    }
});

function hytec_test_sap_pricing_page() {
    ?>
    <div class="wrap">
        <h1>Test SAP Pricing</h1>

        <p>This page tests SAP pricing for a specific product SKU.</p>

        <?php
        if (isset($_POST['test_sku']) && !empty($_POST['test_sku'])) {
            $test_sku = sanitize_text_field($_POST['test_sku']);
            $test_quantity = isset($_POST['test_quantity']) ? max(1, intval($_POST['test_quantity'])) : 1;

            echo '<h2>Testing SAP Pricing for SKU: ' . esc_html($test_sku) . '</h2>';

            // Find the product by SKU
            $product_id = wc_get_product_id_by_sku($test_sku);
            if ($product_id) {
                $product = wc_get_product($product_id);
                $user_id = get_current_user_id();

                echo '<h3>Product Information</h3>';
                echo '<p><strong>Product ID:</strong> ' . $product_id . '</p>';
                echo '<p><strong>Product Name:</strong> ' . $product->get_name() . '</p>';
                echo '<p><strong>WooCommerce Price:</strong> ' . wc_price($product->get_price()) . '</p>';
                echo '<p><strong>Test Quantity:</strong> ' . $test_quantity . '</p>';

                if (function_exists('hytec_sap_get_or_fetch_pricing_with_quantity')) {
                    echo '<h3>SAP Pricing Test</h3>';
                    $pricing = hytec_sap_get_or_fetch_pricing_with_quantity($product, $user_id, $test_quantity);

                    if (is_array($pricing) && isset($pricing['net_value']) && $pricing['net_value'] !== '') {
                        $sap_total_price = (float)$pricing['net_value'] / 100;
                        $sap_unit_price = $sap_total_price / $test_quantity;

                        echo '<div style="background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;">';
                        echo '<p><strong>✅ SAP Pricing Available</strong></p>';
                        echo '<p><strong>SAP Net Value (raw):</strong> ' . $pricing['net_value'] . '</p>';
                        echo '<p><strong>SAP Total Price:</strong> ' . wc_price($sap_total_price) . '</p>';
                        echo '<p><strong>SAP Unit Price:</strong> ' . wc_price($sap_unit_price) . '</p>';

                        // Enhanced tax information
                        if (isset($pricing['sales_tax'])) {
                            $sales_tax_raw = $pricing['sales_tax'];
                            if ($sales_tax_raw !== '' && $sales_tax_raw !== '0' && $sales_tax_raw !== 0) {
                                $sales_tax_decimal = (float)$sales_tax_raw / 100;
                                echo '<p><strong>Sales Tax (raw):</strong> ' . $sales_tax_raw . '</p>';
                                echo '<p><strong>Sales Tax (decimal):</strong> ' . wc_price($sales_tax_decimal) . '</p>';
                            } else {
                                echo '<p><strong>Sales Tax:</strong> <span style="color: orange;">Empty or Zero (' . $sales_tax_raw . ')</span></p>';
                            }
                        } else {
                            echo '<p><strong>Sales Tax:</strong> <span style="color: red;">Not present in response</span></p>';
                        }
                        echo '</div>';

                        echo '<h4>Raw SAP Response</h4>';
                        echo '<pre>' . esc_html(print_r($pricing, true)) . '</pre>';

                    } else {
                        echo '<div style="background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;">';
                        echo '<p><strong>❌ SAP Pricing Not Available</strong></p>';
                        echo '<p>Response: ' . esc_html(print_r($pricing, true)) . '</p>';
                        echo '</div>';
                    }
                } else {
                    echo '<div style="background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;">';
                    echo '<p><strong>⚠️ SAP Pricing Function Not Available</strong></p>';
                    echo '<p>The function <code>hytec_sap_get_or_fetch_pricing_with_quantity</code> is not loaded.</p>';
                    echo '</div>';
                }

            } else {
                echo '<div style="background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;">';
                echo '<p><strong>❌ Product Not Found</strong></p>';
                echo '<p>No product found with SKU: ' . esc_html($test_sku) . '</p>';
                echo '</div>';
            }
        }
        ?>

        <form method="post" style="margin-top: 20px;">
            <table class="form-table">
                <tr>
                    <th scope="row">Product SKU</th>
                    <td>
                        <input type="text" name="test_sku" value="<?php echo isset($_POST['test_sku']) ? esc_attr($_POST['test_sku']) : 'AP02529'; ?>" class="regular-text" />
                        <p class="description">Enter the SKU of the product to test (e.g., AP02529)</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Quantity</th>
                    <td>
                        <input type="number" name="test_quantity" value="<?php echo isset($_POST['test_quantity']) ? intval($_POST['test_quantity']) : 1; ?>" min="1" class="small-text" />
                        <p class="description">Quantity to test pricing for</p>
                    </td>
                </tr>
            </table>
            <?php submit_button('Test SAP Pricing'); ?>
        </form>
    </div>
    <?php
}

function hytec_test_shipto_format_page() {
    ?>
    <div class="wrap">
        <h1>Test Ship-To Address Format</h1>

        <p>This page tests the new nested address format with <code>stateCounty</code> inside the <code>address</code> object.</p>

        <?php
        // Test data with new format
        $new_format_address = [
            "customerId" => "1125565",
            "companyCode" => "10001",
            "identifier" => "Test New Format",
            "isDefaultAddress" => false,
            "companyName" => "Test Company",
            "country" => "US",
            "address" => [
                "street" => "123 Main Street",
                "apartment" => "Suite 100",
                "city" => "Test City",
                "postalCode" => "12345",
                "stateCounty" => "NY"
            ]
        ];

        // Test data with legacy format (for backward compatibility)
        $legacy_format_address = [
            "customerId" => "1125566",
            "companyCode" => "10001",
            "identifier" => "Test Legacy Format",
            "isDefaultAddress" => true,
            "companyName" => "Legacy Company",
            "country" => "US",
            "street" => "456 Oak Avenue",
            "apartment" => null,
            "city" => "Legacy City",
            "postalCode" => "67890",
            "stateCounty" => "CA"  // At root level
        ];

        echo '<h2>Test Data</h2>';

        echo '<h3>New Format (stateCounty nested in address)</h3>';
        echo '<pre>' . esc_html(json_encode($new_format_address, JSON_PRETTY_PRINT)) . '</pre>';

        echo '<h3>Legacy Format (stateCounty at root level)</h3>';
        echo '<pre>' . esc_html(json_encode($legacy_format_address, JSON_PRETTY_PRINT)) . '</pre>';

        // Test conversion if the function exists
        if (function_exists('convert_sap_to_wcmca_format')) {
            echo '<h2>Conversion Results</h2>';

            echo '<h3>New Format → WCMCA</h3>';
            $new_wcmca = convert_sap_to_wcmca_format($new_format_address);
            if (is_wp_error($new_wcmca)) {
                echo '<div class="notice notice-error"><p>Error: ' . $new_wcmca->get_error_message() . '</p></div>';
            } else {
                echo '<pre>' . esc_html(print_r($new_wcmca, true)) . '</pre>';

                // Highlight the state field
                if (!empty($new_wcmca['shipping_state'])) {
                    echo '<p><strong>✅ State Field:</strong> <code>shipping_state</code> = <strong>' . esc_html($new_wcmca['shipping_state']) . '</strong></p>';
                } else {
                    echo '<p><strong>❌ State Field:</strong> <code>shipping_state</code> is empty or missing!</p>';
                }
            }

            echo '<h3>Legacy Format → WCMCA</h3>';
            $legacy_wcmca = convert_sap_to_wcmca_format($legacy_format_address);
            if (is_wp_error($legacy_wcmca)) {
                echo '<div class="notice notice-error"><p>Error: ' . $legacy_wcmca->get_error_message() . '</p></div>';
            } else {
                echo '<pre>' . esc_html(print_r($legacy_wcmca, true)) . '</pre>';

                // Highlight the state field
                if (!empty($legacy_wcmca['shipping_state'])) {
                    echo '<p><strong>✅ State Field:</strong> <code>shipping_state</code> = <strong>' . esc_html($legacy_wcmca['shipping_state']) . '</strong></p>';
                } else {
                    echo '<p><strong>❌ State Field:</strong> <code>shipping_state</code> is empty or missing!</p>';
                }
            }

        } else {
            echo '<div class="notice notice-warning"><p>The <code>convert_sap_to_wcmca_format</code> function is not available. Make sure the SAP ShipTo plugin is active.</p></div>';
        }
        ?>

        <h2>Checkout Form Mapping</h2>
        <p>The <code>shipping_state</code> field from the WCMCA format should automatically map to the checkout form's state field via the existing JavaScript in <code>custom-address-checkout.js</code>.</p>

        <h3>Field Mapping Reference</h3>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>SAP JSON Field</th>
                    <th>WCMCA Database Field</th>
                    <th>Checkout Form Field</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>address.stateCounty</code></td>
                    <td><code>shipping_state</code></td>
                    <td><code>#shipping_state</code></td>
                </tr>
                <tr>
                    <td><code>address.street</code></td>
                    <td><code>shipping_address_1</code></td>
                    <td><code>#shipping_address_1</code></td>
                </tr>
                <tr>
                    <td><code>address.apartment</code></td>
                    <td><code>shipping_address_2</code></td>
                    <td><code>#shipping_address_2</code></td>
                </tr>
                <tr>
                    <td><code>address.city</code></td>
                    <td><code>shipping_city</code></td>
                    <td><code>#shipping_city</code></td>
                </tr>
                <tr>
                    <td><code>address.postalCode</code></td>
                    <td><code>shipping_postcode</code></td>
                    <td><code>#shipping_postcode</code></td>
                </tr>
            </tbody>
        </table>
    </div>
    <?php
}

/**
 * Add admin menu for SAP pricing diagnostics
 */
add_action('admin_menu', 'hytec_add_sap_pricing_admin_menu');
function hytec_add_sap_pricing_admin_menu() {
    add_submenu_page(
        'woocommerce',
        'SAP NET Pricing Diagnostics',
        'SAP Pricing Check',
        'manage_woocommerce',
        'sap-pricing-diagnostics',
        'hytec_sap_pricing_diagnostics_page'
    );
}

/**
 * SAP pricing diagnostics admin page
 */
function hytec_sap_pricing_diagnostics_page() {
    ?>
    <div class="wrap">
        <h1>SAP NET Pricing Diagnostics</h1>

        <?php
        // Handle manual order fix if requested
        if (isset($_POST['fix_order_id']) && current_user_can('manage_woocommerce')) {
            $fix_order_id = intval($_POST['fix_order_id']);
            $result = hytec_manually_fix_sap_order_pricing($fix_order_id);

            if ($result['success']) {
                echo '<div class="notice notice-success"><p>Order #' . $fix_order_id . ' has been fixed. New total: ' . wc_price($result['new_total']) . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Failed to fix order #' . $fix_order_id . ': ' . $result['message'] . '</p></div>';
            }
        }
        ?>

        <div class="card" style="margin-bottom: 20px;">
            <h2>Manual Order Fix</h2>
            <p>If an order is showing WooCommerce prices instead of SAP NET prices, you can manually fix it here:</p>
            <form method="post" style="display: inline-block;">
                <input type="number" name="fix_order_id" placeholder="Order ID" required style="width: 100px;" />
                <input type="submit" value="Fix Order Pricing" class="button button-primary" />
            </form>
        </div>

        <?php
        // Check recent orders with SAP pricing
        global $wpdb;

        $recent_sap_orders = $wpdb->get_results("
            SELECT p.ID, p.post_date, pm1.meta_value as uses_sap_pricing, pm2.meta_value as sap_total
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_uses_sap_pricing'
            LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_sap_calculated_total'
            WHERE p.post_type = 'shop_order'
            AND p.post_status IN ('wc-processing', 'wc-completed', 'wc-on-hold')
            ORDER BY p.post_date DESC
            LIMIT 10
        ");
        ?>

        <div class="card">
            <h2>Recent Orders with SAP Pricing Status</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Date</th>
                        <th>SAP Pricing</th>
                        <th>SAP Total</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_sap_orders as $order_data): ?>
                        <?php
                        $order = wc_get_order($order_data->ID);
                        $uses_sap = $order_data->uses_sap_pricing === 'yes';
                        ?>
                        <tr>
                            <td>
                                <a href="<?php echo admin_url('post.php?post=' . $order_data->ID . '&action=edit'); ?>">
                                    #<?php echo $order_data->ID; ?>
                                </a>
                            </td>
                            <td><?php echo date('Y-m-d H:i', strtotime($order_data->post_date)); ?></td>
                            <td>
                                <?php if ($uses_sap): ?>
                                    <span style="color: #46b450;">✓ SAP NET</span>
                                <?php else: ?>
                                    <span style="color: #d63638;">✗ WooCommerce</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($order_data->sap_total): ?>
                                    <?php echo wc_price($order_data->sap_total); ?>
                                <?php else: ?>
                                    <?php echo wc_price($order->get_total()); ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="#" onclick="checkOrderDetails(<?php echo $order_data->ID; ?>)" class="button button-small">Check Details</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="card" style="margin-top: 20px;">
            <h2>SAP Pricing Summary</h2>
            <?php
            $sap_orders_count = $wpdb->get_var("
                SELECT COUNT(*) FROM {$wpdb->postmeta}
                WHERE meta_key = '_uses_sap_pricing' AND meta_value = 'yes'
            ");

            $total_orders_count = $wpdb->get_var("
                SELECT COUNT(*) FROM {$wpdb->posts}
                WHERE post_type = 'shop_order' AND post_status NOT IN ('trash', 'auto-draft')
            ");
            ?>
            <p><strong>Orders using SAP NET pricing:</strong> <?php echo $sap_orders_count; ?> of <?php echo $total_orders_count; ?> total orders</p>
            <p><strong>Percentage:</strong> <?php echo $total_orders_count > 0 ? round(($sap_orders_count / $total_orders_count) * 100, 1) : 0; ?>%</p>
        </div>

        <div id="order-details" style="margin-top: 20px; display: none;" class="card">
            <h2>Order Details</h2>
            <div id="order-details-content"></div>
        </div>
    </div>

    <script>
    function checkOrderDetails(orderId) {
        document.getElementById('order-details').style.display = 'block';
        document.getElementById('order-details-content').innerHTML = 'Loading...';

        // Make AJAX call to get order details
        jQuery.post(ajaxurl, {
            action: 'get_sap_order_details',
            order_id: orderId,
            nonce: '<?php echo wp_create_nonce('sap_order_details'); ?>'
        }, function(response) {
            if (response.success) {
                document.getElementById('order-details-content').innerHTML = response.data;
            } else {
                document.getElementById('order-details-content').innerHTML = 'Error loading order details.';
            }
        });
    }
    </script>
    <?php
}

/**
 * AJAX handler for getting SAP order details
 */
add_action('wp_ajax_get_sap_order_details', 'hytec_get_sap_order_details_ajax');
function hytec_get_sap_order_details_ajax() {
    if (!wp_verify_nonce($_POST['nonce'], 'sap_order_details') || !current_user_can('manage_woocommerce')) {
        wp_die('Security check failed');
    }

    $order_id = intval($_POST['order_id']);
    $order = wc_get_order($order_id);

    if (!$order) {
        wp_send_json_error('Order not found');
        return;
    }

    $uses_sap_pricing = get_post_meta($order_id, '_uses_sap_pricing', true);
    $sap_calculated_total = get_post_meta($order_id, '_sap_calculated_total', true);
    $pricing_type = get_post_meta($order_id, '_pricing_type', true);

    $html = '<h3>Order #' . $order_id . ' - SAP Pricing Details</h3>';
    $html .= '<p><strong>Uses SAP Pricing:</strong> ' . ($uses_sap_pricing === 'yes' ? 'Yes' : 'No') . '</p>';
    $html .= '<p><strong>Pricing Type:</strong> ' . ($pricing_type ?: 'Not set') . '</p>';
    $html .= '<p><strong>Order Total:</strong> ' . wc_price($order->get_total()) . '</p>';

    if ($sap_calculated_total) {
        $html .= '<p><strong>SAP Calculated Total:</strong> ' . wc_price($sap_calculated_total) . '</p>';
    }

    $html .= '<h4>Line Items:</h4>';
    $html .= '<table class="wp-list-table widefat">';
    $html .= '<thead><tr><th>Product</th><th>SAP Applied</th><th>SAP Unit Price</th><th>SAP Total</th><th>Line Total</th></tr></thead>';
    $html .= '<tbody>';

    foreach ($order->get_items() as $item_id => $item) {
        $sap_applied = $item->get_meta('_sap_pricing_applied');
        $sap_unit_price = $item->get_meta('_sap_unit_price');
        $sap_total_price = $item->get_meta('_sap_total_price');

        $html .= '<tr>';
        $html .= '<td>' . $item->get_name() . '</td>';
        $html .= '<td>' . ($sap_applied === 'yes' ? '✓ Yes' : '✗ No') . '</td>';
        $html .= '<td>' . ($sap_unit_price ? wc_price($sap_unit_price) : '-') . '</td>';
        $html .= '<td>' . ($sap_total_price ? wc_price($sap_total_price) : '-') . '</td>';
        $html .= '<td>' . wc_price($item->get_total()) . '</td>';
        $html .= '</tr>';
    }

    $html .= '</tbody></table>';

    wp_send_json_success($html);
}

/**
 * Manually fix SAP pricing for a specific order
 */
function hytec_manually_fix_sap_order_pricing($order_id) {
    $order = wc_get_order($order_id);
    if (!$order) {
        return array('success' => false, 'message' => 'Order not found');
    }

    $sap_total = 0;
    $fixed_items = 0;

    // Check each line item for SAP pricing data
    foreach ($order->get_items() as $item_id => $item) {
        $sap_pricing_applied = $item->get_meta('_sap_pricing_applied');
        $sap_total_price = $item->get_meta('_sap_total_price');

        if ($sap_pricing_applied === 'yes' && $sap_total_price) {
            // Apply SAP pricing to the line item
            $item->set_subtotal($sap_total_price);
            $item->set_total($sap_total_price);
            $item->save();

            $sap_total += (float)$sap_total_price;
            $fixed_items++;
        } else {
            // No SAP pricing available, use current total
            $sap_total += (float)$item->get_total();
        }
    }

    if ($fixed_items > 0) {
        // Recalculate order totals
        $order->calculate_totals(false);
        $order->save();

        // Update order meta
        update_post_meta($order_id, '_uses_sap_pricing', 'yes');
        update_post_meta($order_id, '_sap_calculated_total', $sap_total);
        update_post_meta($order_id, '_pricing_type', 'SAP_NET');

        // Add order note
        $order->add_order_note(
            sprintf('SAP NET pricing manually applied to %d line item(s). Order total updated to %s.',
                $fixed_items,
                wc_price($order->get_total())
            ),
            false,
            true
        );

        return array(
            'success' => true,
            'new_total' => $order->get_total(),
            'fixed_items' => $fixed_items
        );
    } else {
        return array('success' => false, 'message' => 'No SAP pricing data found for this order');
    }
}