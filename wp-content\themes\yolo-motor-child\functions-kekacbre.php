<?php

function yolo_enqueue_additional_styles()
{
    wp_enqueue_style('child-style', get_stylesheet_directory_uri() . '/assets/css/net.css');
}
add_action('wp_enqueue_scripts', 'yolo_enqueue_additional_styles', 9999);

add_action('wp_ajax_nopriv_empty_cart', 'custom_empty_cart');
add_action('wp_ajax_empty_cart', 'custom_empty_cart');
function custom_empty_cart()
{
    WC()->cart->empty_cart();
    wp_send_json_success('Your Cart is empty now');
}

add_action('wp_ajax_nopriv_cart_items_count', 'cart_items_count');
add_action('wp_ajax_cart_items_count', 'cart_items_count');
function cart_items_count()
{
    $items = count( WC()->cart->get_cart() );
    echo $items;

    wp_die(); // Always use wp_die() after an AJAX function to end execution.
}

add_action('wp_ajax_woocommerce_ajax_add_to_cart', 'custom_woocommerce_ajax_add_to_cart');
add_action('wp_ajax_nopriv_woocommerce_ajax_add_to_cart', 'custom_woocommerce_ajax_add_to_cart');

function custom_woocommerce_ajax_add_to_cart() {
    if (!isset($_POST['product_id']) || !isset($_POST['quantity'])) {
        wp_send_json_error(['error' => 'Product ID or quantity is missing.']);
        wp_die();
    }

    $product_id = intval($_POST['product_id']);
    $quantity = intval($_POST['quantity']);

    $added = WC()->cart->add_to_cart($product_id, $quantity);

    if ($added) {
        wp_send_json_success(['message' => 'Product added to cart successfully.']);
    } else {
        wp_send_json_error(['error' => 'Failed to add product to cart.']);
    }

    wp_die();
}

function custom_woocommerce_button_label_script() {
    // Register and enqueue the custom script
    wp_enqueue_script(
        'custom-woocommerce-button-label',
        get_template_directory_uri() . '/js/custom-woocommerce-button-label.js', // Adjust path as needed
        array( 'wp-element', 'wc-blocks-checkout' ), // wc-blocks-checkout dependency
        null,
        true
    );
}
add_action( 'wp_enqueue_scripts', 'custom_woocommerce_button_label_script' );

// add_action('woocommerce_after_order_notes', 'add_purchase_order_number_field');

// function add_purchase_order_number_field($checkout)
// {
//     woocommerce_form_field('purchase_order_number', array(
//         'type' => 'text',
//         'class' => array('form-row-wide'),
//         'label' => __('Purchase Order Number'),
//         'placeholder' => __('Enter Purchase Order Number'),
//         'required' => true,
//     ), $checkout->get_value('purchase_order_number'));
// }

add_action('woocommerce_checkout_process', 'validate_purchase_order_number_field');

function validate_purchase_order_number_field()
{
    if (!$_POST['purchase_order_number']) {
        wc_add_notice(__('Please enter a Purchase Order Number.'), 'error');
    } else {
        // Check if the purchase order number already exists
        $entered_pon = sanitize_text_field($_POST['purchase_order_number']);
        $args = array(
            'post_type'   => 'shop_order',
            'post_status' => 'any',
            'meta_query'  => array(
                array(
                    'key'   => '_purchase_order_number',
                    'value' => $entered_pon,
                    'compare' => '=',
                ),
            ),
        );
        $existing_orders = get_posts($args);
        if (!empty($existing_orders)) {
            wc_add_notice(__('This Purchase Order Number is already in use. Please enter a unique number.'), 'error');
        }
    }
}

add_action('woocommerce_checkout_update_order_meta', 'save_purchase_order_number_field');

function save_purchase_order_number_field($order_id)
{
    if (!empty($_POST['purchase_order_number'])) {
        update_post_meta($order_id, '_purchase_order_number', sanitize_text_field($_POST['purchase_order_number']));
    }
}

add_action('woocommerce_after_order_notes', 'add_custom_fields_after_order_notes');
function add_custom_fields_after_order_notes($checkout) {
    echo '<h3 class="additional_order_title">Additional Order Information</h3>';
    echo '<div class="additional_order_wrap">';   
        // Customer Requested Ship Date
        echo '<div class="form-row-row">';
        woocommerce_form_field('purchase_order_number', array(
            'type' => 'text',
            'class' => array('form-row-wide form-row-half'),
            'label' => __('Purchase Order Number'),
            'placeholder' => __('Enter Purchase Order Number'),
            'required' => true,
        ), $checkout->get_value('purchase_order_number'));
        woocommerce_form_field('customer_requested_ship_date', array(
            'type'        => 'date',
            'class'       => array('form-row-wide form-row-half'),
            'label'       => __('Customer Requested Ship Date'),
            'required'    => true,
            'custom_attributes' => array(
                'min' => date('Y-m-d'), // Set minimum date to today
            ),
        ), $checkout->get_value('customer_requested_ship_date'));
        echo '</div>';

        // Second Row: Shipping Account Number
        echo '<div class="form-row-row">';
// Restored shipping method preference dropdown
woocommerce_form_field('shipping_method_preference', array(
    'type'     => 'select',
    'class'    => array('form-row-wide form-row-half select_shipping'),
    'label'    => __('Shipping Method Preference'),
    'required' => true,
    'options'  => array(
        '' => __('Select a Shipping Method'),
        '0'  => 'See Shipping Instr.',
        '1'  => 'Standard - Generic',
        '02' => 'Rush Orders',
        '03' => 'FDX Priority Overnight',
        '04' => 'FDX First Overnight',
        '05' => 'FDX Second Day',
        '06' => 'FDX Std Overnight',
        '7'  => 'FDX International Ec',
        '8'  => 'FDX Intern Ecn Frght',
        '9'  => 'FDX Intern First',
        '0A' => 'DHL',
        '0B' => 'UPS',
        '0C' => 'Pickup',
        '10' => 'FDX Intern Priority',
        '11' => 'FDX Int Priority Frt',
        '12' => 'FDX Ground',
        '13' => 'FDX 1st Day Freight',
        '14' => 'FDX 2nd Day Freight',
        '15' => 'FDX 3rd Day Freight',
        '16' => 'FDX Express Saver',
        '17' => 'FDX Residential',
        '18' => 'UPS Next Day Air',
        '19' => 'UPS NDA Saver',
        '20' => 'UPS ND Early AM',
        '21' => 'UPS Second Day Air',
        '22' => 'UPS 2nd Day Air A.M.',
        '23' => 'UPS SonicAir Best Fl',
        '24' => 'UPS Three Day Select',
        '25' => 'UPS Ground',
        '26' => 'UPS CA Expedited',
        '27' => 'UPS CA Express',
        '28' => 'UPS CA Express Plus',
        '29' => 'UPS Standard for CA',
        '30' => 'UPS Intl Expedited',
        '31' => 'UPS Intl Express',
        '32' => 'UPS ND Saturday Air',
        '33' => 'DHL ND 10:30 AM',
        '34' => 'DHL ND 12:00 pm',
        '35' => 'DHL Next Day 3:00 PM',
        '36' => 'DHL 2nd Day',
        '37' => 'DHL ND 10:30 Letter',
        '38' => 'DHL ND 12:00 Letter',
        '39' => 'DHL ND 3:00 Letter',
        '40' => 'DHL 2nd Day Letter',
        '41' => 'DHL Ground',
        '42' => 'DHL Medml@Home Def',
        '43' => 'DHL Medml@Home Std',
        '44' => 'USPS BPM@Home Def',
        '45' => 'USPS BPM @Home Std',
        '46' => 'USPS@Home Def',
        '47' => 'USPS@Home Std',
        '48' => 'DHL Global Mail Prio',
        '49' => 'DHL Global Mail Std',
        '50' => 'DHL Int Document',
        '51' => 'DHL Int Document Ltr',
        '52' => 'DHL Wrld Wd Priorty',
        '53' => 'ABF Freight Sys Std',
        '55' => 'FedEx Freight Std',
        '56' => 'DHL Wrld Wd Express',
        '57' => 'New Eng Motor Fr Std',
        '59' => 'Old Dominion Std',
        '61' => 'CCX Standard',
        '62' => 'CCX Guaranteed',
        '63' => 'Central Trpt Int Std',
        '65' => 'UPS Supply Chain AM',
        '66' => 'UPS Supply Chain 2DY',
        '67' => 'UPS Supply Chain ECO',
        '68' => 'UPS Supply Chain STD',
        '69' => 'Customer Pickup',
        '70' => 'Generic Carrier',
        '71' => 'Cavalier Transport',
        '72' => 'UPS Supply Chain',
        '73' => 'BAX Global',
        '74' => 'Air Freight',
        '75' => 'Sea Freight',
        '76' => 'Ground',
        '77' => 'Next Day Air',
        '78' => '2nd Day Air',
        '79' => '3rd Day Air',
        '80' => 'International Expres',
        '81' => 'International Priori',
        '82' => 'Ground Express',
        '83' => 'Counter To Counter',
        '84' => 'Economy',
        '85' => 'Saturday Delivery',
        '86' => 'Expedited Service',
        '87' => 'Express',
        '88' => 'Air Express',
        '89' => 'Less Container Load',
        '90' => 'Full Container Load',
        '91' => 'Less Trailer Load Ro',
        '92' => 'Full Trailer Load Ro',
        '93' => 'Courier',
        '94' => 'Next day 7 am',
        '95' => 'Next day 9 am',
        '96' => 'Next day 10.30 am',
        '97' => 'Next day 12 noon',
        'A1' => 'Apex',
        'A2' => 'UPS AirFrt 2 Day',
        'A4' => 'Alleghany Plant Serv',
        'AG' => 'UPS AirFrt Nxt Guar',
        'AU' => 'UPS AirFrt Nxt Day',
        'AX' => 'AXFLOW Agreement',
        'B0' => 'Bestway Gnd',
        'B1' => 'Bestway Nxt Day Air',
        'B2' => 'Bestway 2 Day Air',
        'B3' => 'Bestway',
        'BT' => 'BI-Weekly Ship Thurs',
        'C0' => '3rd Thurs of Month',
        'C1' => 'Monday Only',
        'C2' => 'Tuesday Only',
        'C3' => 'Wednesday Only',
        'C4' => 'Thursday Only',
        'C5' => 'Friday Only',
        'C6' => 'Tues/Frid Consol.',
        'C7' => 'Mon/Thurs Consol.',
        'C8' => '2nd&4th Friday of md',
        'C9' => 'Bi-Weekly Shp Tues',
        'CA' => 'First Wed of Month',
        'CB' => 'Tuesday & Thursday',
        'CI' => 'UPS AirFrt Con Int',
        'D0' => 'DHL Ground',
        'D1' => 'DHL Exp Dom -10:30am',
        'D2' => 'DHL Exp Dom - 12:00',
        'D3' => 'DHL Exp Dom - 3:00pm',
        'D4' => 'DHL Exp Dom - 2 Day',
        'D5' => 'Day&Ross',
        'DA' => 'DHL GFwd Int AirFrt',
        'DI' => 'DHL Exp Int Sml Pkg',
        'DM' => 'Dallas Mavis',
        'DO' => 'DHL GFwd Int OcnFrt',
        'E1' => 'Eclipse',
        'EI' => 'FedEx Exp Int Prty',
        'EX' => 'Export Orders Std.',
        'F0' => 'FedEx Express Gnd',
        'F1' => 'FedEx AirFrt Nxt Day',
        'F2' => 'FedEx AirFrt 2 Day',
        'F3' => 'FedEx Express 2 Day',
        'FC' => 'FedEx Ground -Canada',
        'FE' => 'FedEx Economy',
        'FO' => 'FedEx Express Std-PM',
        'FP' => 'FedEx Exp Prty-AM',
        'FS' => 'FedEx Express Saver',
        'G1' => 'DHL GFwd Dom Nxt Air',
        'G2' => 'DHL GFwd Dom 2 Air',
        'GD' => 'GD CHOICE',
        'H1' => 'BOC Air',
        'H2' => 'Central Freight Line',
        'H3' => 'Ceva Logistics',
        'H4' => 'Dayton Frt Line',
        'H5' => 'Dawes Transport',
        'H6' => 'DOHRN',
        'H7' => 'Echo Logistics',
        'H8' => 'Kuehne-Nagel',
        'H9' => 'Lakeville Motor Expr',
        'IA' => 'UPS AirFrt Dir - Int',
        'IE' => 'UPS Exp Frt - Int',
        'IN' => 'Invoice only',
        'IO' => 'UPS SCS Int Ocn Std',
        'LT' => 'LTL/Truck Carriers',
        'M1' => 'Midland',
        'N0' => 'UPS Expedited',
        'N1' => 'Consolidated Shp',
        'N2' => 'Normal Truck',
        'N3' => 'Truck and Ferry',
        'N4' => 'Technician',
        'N5' => 'Night Star Express',
        'N6' => 'UPS Express Saver',
        'N7' => 'UPS Standard',
        'N8' => 'UPS Express',
        'N9' => 'UPS Express Plus',
        'NA' => 'TNT',
        'NF' => 'UPS AirFrt Nxt Out',
        'NG' => 'DHL Europe',
        'NH' => 'DHL Non Europe',
        'NI' => 'DHL Documents',
        'NJ' => 'DHL 9.00 AM',
        'NK' => 'DHL12.00 pm',
        'NL' => 'Free of Charge',
        'P1' => 'PILOT Nxt Day Air',
        'P2' => 'PILOT 2 Day Air',
        'PA' => 'PILOT Int AirFrt',
        'PO' => 'PILOT Int OceanFrt',
        'R1' => 'Reimer',
        'SA' => 'Pchenker Int Air Std',
        'SO' => 'Schenker Int Ocean',
        'T1' => 'N&M TRANSFER',
        'T2' => 'Rotra',
        'T3' => 'Standard Forwarding',
        'T4' => 'TAX AIR',
        'T5' => 'Unishippers',
        'TC' => 'TCI Transport',
        'TF' => 'Spec Equip/Flatbed',
        'TH' => 'Special Generic Othe',
        'TL' => 'FTL/Truck Carriers',
        'TT' => 'Tryon Transport',
        'U0' => 'UPS Gnd Commercial',
        'U1' => 'UPS Nxt Day Air',
        'U2' => 'UPS 2 Day Air',
        'U3' => 'UPS 3 Day Select',
        'UA' => 'UPS Nxt Day Air Erly',
        'UC' => 'UPS Canada - Std',
        'WA' => 'UPS WW Exp Plus CA',
        'WC' => 'UPS WW Express CA',
        'WD' => 'UPS WW Expedited CA',
        'WE' => 'UPS WW Express',
        'WP' => 'UPS WW Express Plus',
        'WS' => 'UPS WW Saver',
        'WV' => 'UPS WW Saver CA',
        'WX' => 'UPS WW Expedited',
        'X1' => 'Export Orders Spec 1',
        'X2' => 'Export Orders Spec 2',
        'X3' => 'Export Orders Spec 3',
        'X4' => 'Export Orders Spl4',
        'XA' => 'A Duie Pyle',
        'XB' => 'AAA Cooper',
        'XC' => 'ATI Trans',
        'XD' => 'Averitt',
        'XE' => 'Benton Freight',
        'XF' => 'CH Robinson',
        'XG' => 'Conway Southern',
        'XH' => 'DSV Air and Sea',
        'XI' => 'Estes',
        'XJ' => 'Lynden Air',
        'XK' => 'Nippon Express',
        'XL' => 'R&L Carrier',
        'XM' => 'Roadway',
        'XN' => 'SAIA',
        'XO' => 'Southeastern',
        'XP' => 'USF Holland',
        'XR' => 'UTI Logistics',
        'XS' => 'Vitran Express',
        'XT' => 'Ward Trucking',
        'XU' => 'Wilson',
        'XV' => 'Yellow Roadway Corp.',
        'XW' => 'Fedex Priority (LTL)',
        'XX' => 'UPS Freight (LTL )',
        'YA' => 'Canadian Freightways',
        'YC' => 'Dicom',
        'YE' => 'EPIC Transport',
        'YG' => 'Guilbeault Transport',
        'YI' => 'Kingsway/Cabano',
        'YK' => 'Manitoulin',
        'YM' => 'Purolator 9 a.m.',
        'YO' => 'Purolator Air',
        'YQ' => 'Purolator Ground',
        'YS' => 'Robert Transport',
        'YU' => 'Sameday air',
        'YW' => 'Sameday ground',
        'YY' => 'TST Transport',
        'Z1' => 'Hellmans',
        'Z2' => 'XPO Logistics 500430',
        'Z3' => 'Jarrett Logist500428',
        'Z4' => 'ESHIPPING 500429',
        'Z5' => 'Best Transpt.500427',
        'Z9' => 'Monthly 4th Thursday',
        'ZL' => 'Rockford M/W/F Consl',
        'ZM' => 'Rockford Tu/Th Consl',
        'ZN' => 'Rockford We/Fr Consl',
        'ZO' => 'Rockford Bi-Wkly Wed',
        'ZS' => 'Non Priority',
        'ZZ' => 'To be informed'
    )
), $checkout->get_value('shipping_method_preference'));
        woocommerce_form_field('shipping_account_number', array(
    'type'     => 'select',
    'class'    => array('form-row-wide form-row-half select_shipping'),
    'label'    => __('Shipping Freight Terms'),
    'required' => true,
    'options'  => array(
        ''  => __('Select Shipping Freight Term'),
        'C' => 'Freight Collect',
        'D' => 'Customer Pickup',
        'F' => 'Free Freight',
        'P' => 'Prepay & Add Invoice',
        'T' => 'Third Party Pays',
        'Z' => 'See Shipping Text',
    )
), $checkout->get_value('shipping_account_number'));
        echo '</div>';
        // echo '<div class="form-row-row">';
        //     echo '<p class="form-row form-row-wide form-row-half">';
        //         echo '<label for="additional_files">' . __('Upload Additional Files') . '</label>';
        //         echo '<input type="file" name="additional_files" id="additional_files" accept=".xlsx, .xls, .pdf, .csv, .doc, .docx, .ppt, .pptx"/>';
        //     echo '</p>';
        // // woocommerce_form_field('additional_files', array(
        // //     'type'        => 'file',
        // //     'label'       => __('Upload Additional File'),
        // //     'required'    => false,
        // // ), '');
        // echo '</div>';

        // Third Row: Special Requests (Full Width)
        echo '<div class="form-row-row">';
        woocommerce_form_field('special_requests', array(
            'type'        => 'textarea',
            'class'       => array('form-row-wide custom-notee'),
            'label'       => __('Special Requests'),
            'required'    => false,
            'placeholder' => __('(Note: Any text entered in this box will place your order on hold until a customer service representative can review your request. Please do not reiterate information already entered elsewhere in the order.)'),
            //'description' => __('Note: Any text entered in this box will place your order on hold until a customer service representative can review your request. Please do not reiterate information already entered elsewhere in the order.'),
            'custom_attributes' => array(
                'rows' => '4',
                'cols' => '100'
            )
        ), $checkout->get_value('special_requests'));
        echo '</div>';

    echo '</div>';
}

add_action('woocommerce_checkout_update_order_meta', 'save_custom_checkout_fields');
function save_custom_checkout_fields($order_id) {
    if (!empty($_POST['purchase_order_number'])) {
        update_post_meta($order_id, '_purchase_order_number', sanitize_text_field($_POST['purchase_order_number']));
    }

    if (!empty($_POST['customer_requested_ship_date'])) {
        update_post_meta($order_id, '_customer_requested_ship_date', sanitize_text_field($_POST['customer_requested_ship_date']));
    }

    if (!empty($_POST['shipping_method_preference'])) {
        update_post_meta($order_id, '_shipping_method_preference', sanitize_text_field($_POST['shipping_method_preference']));
    }

    if (!empty($_POST['shipping_account_number'])) {
        update_post_meta($order_id, '_shipping_account_number', sanitize_text_field($_POST['shipping_account_number']));
    }

    if (!empty($_POST['special_requests'])) {
        update_post_meta($order_id, '_special_requests', sanitize_textarea_field($_POST['special_requests']));

        // If special requests are provided, put the order on hold
        $order = wc_get_order($order_id);
        if ($order) {
            $order->update_status('on-hold', __('Order placed on hold due to special requests requiring customer service review.'));
        }
    }

    // my_plugin_custom_log("============= Files ===============: ". print_r($_FILES, true));

    // if (isset($_FILES['additional_files']) && $_FILES['additional_files']['size'] > 0) {
    //     $upload = wp_upload_bits($_FILES['additional_files']['name'], null, file_get_contents($_FILES['additional_files']['tmp_name']));
    //     if (!$upload['error']) {
    //         update_post_meta($order_id, '_additional_files', esc_url($upload['url']));
    //     }
    // }
}

// Display the uploaded file URL on the WooCommerce Admin Order Page
function custom_display_uploaded_file_in_admin_order($order) {
    $file_url = get_post_meta($order->get_id(), '_additional_files', true);

    echo '<h3 style="margin-top: 20px;float: left;margin-bottom: 0px;width: 100%;">' . __('Additional Files:', 'woocommerce') . '</h3> ';
    if ($file_url) {
        echo '<p style="margin-top: 10px;float: left;"><a href="' . esc_url($file_url) . '" target="_blank">' . esc_html(basename($file_url)) . '</a></p>';
    } else {
        echo '<p style="margin-top: 10px;float: left;">' . __('No file uploaded.', 'woocommerce') . '</p>';
    }
}
add_action('woocommerce_admin_order_data_after_order_details', 'custom_display_uploaded_file_in_admin_order');


add_filter('woocommerce_email_attachments', 'attach_csv_to_completed_order_email_additional', 10, 4);
function attach_csv_to_completed_order_email_additional($attachments, $email_id, $order, $email)
{
    if ($email_id  === 'new_order' && is_a($order, 'WC_Order') && is_a($email, 'WC_Email_New_Order')) {
        $order_id = $order->get_id(); // Get the order ID
        $additional_file = get_post_meta($order_id, '_additional_files', true);
        
        if( $additional_file !== ""){
            $attachments[] = $additional_file;
        }
    }
    return $attachments;
}

add_action('woocommerce_admin_order_data_after_billing_address', 'display_custom_checkout_fields_in_admin', 10, 1);

function display_custom_checkout_fields_in_admin($order) {
    $ship_date = get_post_meta($order->get_id(), '_customer_requested_ship_date', true);
    $ship_method = get_post_meta($order->get_id(), '_shipping_method_preference', true);
    $ship_account = get_post_meta($order->get_id(), '_shipping_account_number', true);
    $purchase_order_number = get_post_meta($order->get_id(), '_purchase_order_number', true);
    $special_requests = get_post_meta($order->get_id(), '_special_requests', true);

    if ($purchase_order_number) {
        echo '<p><strong>' . __('Purchase Order Number:') . '</strong> ' . esc_html($purchase_order_number) . '</p>';
    }
    if ($ship_date) {
        echo '<p><strong>' . __('Customer Requested Ship Date:') . '</strong> ' . esc_html($ship_date) . '</p>';
    }
    if ($ship_method) {
        echo '<p><strong>' . __('Shipping Method Preference:') . '</strong> ' . esc_html($ship_method) . '</p>';
    }
    if ($ship_account) {
        echo '<p><strong>' . __('Shipping Account Number:') . '</strong> ' . esc_html($ship_account) . '</p>';
    }
    if ($special_requests) {
        echo '<p><strong>' . __('Special Requests:') . '</strong><br>' . nl2br(esc_html($special_requests)) . '</p>';
    }
}

// Add custom fields to the order email
add_action('woocommerce_email_after_order_table', 'add_custom_fields_to_order_email', 10, 4);

function add_custom_fields_to_order_email($order, $sent_to_admin, $plain_text, $email) {
    $ship_date = get_post_meta($order->get_id(), '_customer_requested_ship_date', true);
    $ship_method = get_post_meta($order->get_id(), '_shipping_method_preference', true);
    $ship_account = get_post_meta($order->get_id(), '_shipping_account_number', true);
    $purchase_order_number = get_post_meta($order->get_id(), '_purchase_order_number', true);
    $special_requests = get_post_meta($order->get_id(), '_special_requests', true);

    // Get export compliance status for non-US customers
    $export_compliance = get_post_meta($order->get_id(), '_export_compliance_agreed', true);
    $export_compliance_date = get_post_meta($order->get_id(), '_export_compliance_date', true);

    // Determine if shipping destination is US or non-US
    $shipping_country = $order->get_shipping_country();
    $is_us_shipping = (strtoupper((string) $shipping_country) === 'US');

    if ($ship_date || $ship_method || $ship_account) {
        echo '<h3>Shipping Information</h3>';
        echo '<table cellspacing="0" cellpadding="6" style="width: 100%; border: 1px solid #e5e5e5;margin-bottom: 20px;" border="1">';
        echo '<tbody>';

        if ($purchase_order_number) {
            echo '<tr>';
            echo '<th style="text-align: left;">Purchase Order Number:</th>';
            echo '<td style="text-align: left;">' . esc_html($purchase_order_number) . '</td>';
            echo '</tr>';
        }

        if ($ship_date) {
            echo '<tr>';
            echo '<th style="text-align: left;">Customer Requested Ship Date:</th>';
            echo '<td style="text-align: left;">' . esc_html($ship_date) . '</td>';
            echo '</tr>';
        }

        if ($ship_method) {
            echo '<tr>';
            echo '<th style="text-align: left;">Shipping Method Preference:</th>';
            echo '<td style="text-align: left;">' . esc_html($ship_method) . '</td>';
            echo '</tr>';
        }

        if ($ship_account) {
            echo '<tr>';
            echo '<th style="text-align: left;">Shipping Freight Terms:</th>';
            echo '<td style="text-align: left;">' . esc_html($ship_account) . '</td>';
            echo '</tr>';
        }

        // Add Export Compliance for non-US shipping destinations
        if (!$is_us_shipping && $export_compliance) {
            echo '<tr>';
            echo '<th style="text-align: left;">Export Compliance:</th>';
            echo '<td style="text-align: left;">Agreed';
            if ($export_compliance_date) {
                echo ' (' . esc_html(date('Y-m-d H:i:s', strtotime($export_compliance_date))) . ')';
            }
            echo '</td>';
            echo '</tr>';
        }

        // Add Special Requests
        if ($special_requests) {
            echo '<tr>';
            echo '<th style="text-align: left;">Special Requests:</th>';
            echo '<td style="text-align: left;">' . nl2br(esc_html($special_requests)) . '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }
}

// Display custom fields on the My Account > Orders > Single Order page
add_action('woocommerce_order_details_after_order_table', 'display_custom_fields_on_single_order_page', 10, 1);

function display_custom_fields_on_single_order_page($order) {
    $ship_date = get_post_meta($order->get_id(), '_customer_requested_ship_date', true);
    $ship_method = get_post_meta($order->get_id(), '_shipping_method_preference', true);
    $ship_account = get_post_meta($order->get_id(), '_shipping_account_number', true);
    $purchase_order_number = get_post_meta($order->get_id(), '_purchase_order_number', true);
    $special_requests = get_post_meta($order->get_id(), '_special_requests', true);

    if ($ship_date || $ship_method || $ship_account) {
        echo '<h3 style="margin-bottom: 20px;">Shipping Information</h3>';
        echo '<table class="shipping-table-my-account" cellspacing="0" cellpadding="6" style="width: 100%;" border="0">';
        echo '<tbody>';

        if ($purchase_order_number) {
            echo '<tr>';
            echo '<th style="text-align: left;">Purchase Order Number:</th>';
            echo '<td style="text-align: left;">' . esc_html($purchase_order_number) . '</td>';
            echo '</tr>';
        }

        if ($ship_date) {
            echo '<tr>';
            echo '<th style="text-align: left;">Customer Requested Ship Date:</th>';
            echo '<td style="text-align: left;">' . esc_html($ship_date) . '</td>';
            echo '</tr>';
        }

        if ($ship_method) {
            echo '<tr>';
            echo '<th style="text-align: left;">Shipping Method Preference:</th>';
            echo '<td style="text-align: left;">' . esc_html($ship_method) . '</td>';
            echo '</tr>';
        }

        if ($ship_account) {
            echo '<tr>';
            echo '<th style="text-align: left;">Shipping Account Number:</th>';
            echo '<td style="text-align: left;">' . esc_html($ship_account) . '</td>';
            echo '</tr>';
        }

        if ($special_requests) {
            echo '<tr>';
            echo '<th style="text-align: left;">Special Requests:</th>';
            echo '<td style="text-align: left;">' . nl2br(esc_html($special_requests)) . '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }
}

add_action('woocommerce_before_edit_account_form', 'add_custom_panels_above_edit_account_form');

function add_custom_panels_above_edit_account_form() {
    // Fetch current user ID and their metadata
    $user_id = get_current_user_id();
    $company_name = get_user_meta($user_id, 'company_name', true); // Replace with the correct meta key for company name
    $customer_number = get_user_meta($user_id, '_customer', true);
    $region = get_user_meta($user_id, 'region', true); // Replace with the correct meta key for region
    $address = get_user_meta($user_id, 'address', true); // Replace with the correct meta key for address
    $address_2 = get_user_meta($user_id, 'address_2', true); // Replace with the correct meta key for address 2
    $city = get_user_meta($user_id, 'city', true); // Replace with the correct meta key for city
    $state = get_user_meta($user_id, 'state', true); // Replace with the correct meta key for state
    $country = get_user_meta($user_id, 'country', true); // Replace with the correct meta key for country
    $postal_code = get_user_meta($user_id, 'postal_code', true); // Replace with the correct meta key for postal code

    // Fetch user details
    $user_info = get_userdata($user_id);
    $first_name = $user_info->first_name;
    $last_name = $user_info->last_name;
    $email = $user_info->user_email;

    // Output the custom panels
    ?>
    <div class="custom-panels-container" style="display: flex; gap: 20px; margin-bottom: 20px;max-width: 750px;">
        <!-- First Panel -->
        <div class="company-details-panel" style="flex: 1;">
            <h3>Company Details</h3>
            <div class="gray-panel">
                <p class="mb-1"><strong></strong> <?php echo esc_html($company_name ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>Customer Number:</strong> <?php echo esc_html($customer_number ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>Region:</strong> <?php echo esc_html($region ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>Address:</strong> <?php echo esc_html($address ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>Address 2:</strong> <?php echo esc_html($address_2 ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>City:</strong> <?php echo esc_html($city ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>State:</strong> <?php echo esc_html($state ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>Country:</strong> <?php echo esc_html($country ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>Postal Code:</strong> <?php echo esc_html($postal_code ?: 'N/A'); ?></p>
            </div>
        </div>

        <!-- Second Panel -->
        <div class="additional-panel" style="flex: 1;">
            <h3>Company Contact</h3>
            <div class="gray-panel">
                <p class="mb-1"><strong>Name:</strong> <?php echo esc_html($first_name . ' ' . $last_name ?: 'N/A'); ?></p>
                <p class="mb-1"><strong>Email:</strong> <?php echo esc_html($email ?: 'N/A'); ?></p>
            </div>
        </div>
    </div>
    <?php
}


function wpc_show_admin_bar() {
    $only_roles = [ 'administrator'/* , 'b2b_administrator' */ ];
    return is_user_logged_in() && array_intersect( wp_get_current_user()->roles, $only_roles);
}
add_filter('show_admin_bar', 'wpc_show_admin_bar');



// add_action('enqueue_block_assets', function () {
//     wp_enqueue_script(
//         'custom-woocommerce-cart',
//         get_template_directory_uri() . '/js/custom-woocommerce-cart.js',
//         ['wc-blocks-cart'], // Ensure WooCommerce Blocks dependencies are loaded
//         null,
//         true
//     );
// });

add_action('template_redirect', 'redirect_logged_in_users_from_custom_login');

function redirect_logged_in_users_from_custom_login() {
    // Replace 'custom-login-slug' with the slug of your custom login page
    if (is_user_logged_in() && is_page('login')) {
        // Change this URL to the page you want to redirect logged-in users to
        wp_redirect(home_url('/my-account')); 
        exit;
    }
}

add_action('wp_footer', 'add_shop_button_to_h1_my_account');
function add_shop_button_to_h1_my_account() {
    if (is_account_page()) { // Ensure this only runs on the My Account page
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // Select the first <h1> on the My Account page
                const header = document.querySelector('h1');
                if (header) {
                    // Create the shop button
                    <?php if(is_user_logged_in()){ ?>
                    const shopButton = document.createElement('a');
                    shopButton.href = '/shop';
                    shopButton.textContent = 'VIEW PRODUCTS';
                    shopButton.className = 'btn btn-wide orange-bg white px-2 f-12 px-mob-2';
                    // Add the button inside the <h1>, after its text
                    header.appendChild(shopButton);
                    <?php } ?>

                    <?php if(is_wc_endpoint_url('lost-password')){ ?>
                        header.textContent = 'Lost Password';
                    <?php } ?>

                }
            });
        </script>
        <?php
    }
    if(!is_user_logged_in() AND is_page('login')){ ?>
        <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 'Email / Username'
            jQuery('#loginform .login-username input').attr('placeholder', 'Email / Username');
            jQuery('#loginform .login-password input').attr('placeholder', 'Password');
        });
        </script>
    <?php }

}
// shopButton.textContent = 'VIEW PRODUCTS';
// shopButton.className = 'btn btn-wide orange-bg white px-5 f-12';

function restrict_access_except_login_and_woocommerce_lost_password() {
    // Allow access if the user is logged in
    if (is_user_logged_in()) {
        return;
    }

    // Allow access to the custom login page
    if (is_page('login')) {
        return;
    }

    // Allow access to the WooCommerce lost password page
    if (is_wc_endpoint_url('lost-password')) {
        return;
    }

    // Allow AJAX and REST API requests
    if (defined('DOING_AJAX') && DOING_AJAX) {
        return;
    }

    if (strpos($_SERVER['REQUEST_URI'], '/wp-json/') !== false) {
        return;
    }

    // Redirect non-logged-in users to the login page
    wp_redirect(home_url('/login'));
    exit;
}
add_action('template_redirect', 'restrict_access_except_login_and_woocommerce_lost_password');



add_action( 'login_form_middle', 'add_lost_password_link' );
function add_lost_password_link() {
    return '<a href="/my-account/lost-password/" class="wplf-lostpassword">Forgot Your Password?</a>';
}

// function custom_login_page(){
//     $args = array(
//         'echo'           => true,
//         'remember'       => true,
//         'redirect'       => ( is_ssl() ? 'https://' : 'http://' ) . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'],
//         'form_id'        => 'loginform',
//         'id_username'    => 'user_login',
//         'id_password'    => 'user_pass',
//         'id_remember'    => 'rememberme',
//         'id_submit'      => 'wp-submit',
//         'label_username' => __( 'Username or Email Address' ),
//         'label_password' => __( 'Password' ),
//         'label_remember' => __( 'Remember Me' ),
//         'label_log_in'   => __( 'Log In' ),
//         'value_username' => '',
//         'value_remember' => false
//     );
//     wp_login_form($args);
//     add_lost_password_link();
// }
// add_shortcode('custom-login-page', 'custom_login_page');

/* function custom_login_form_shortcode($atts) {
    // Start output buffering
    ob_start();

    // Handle login errors
    $login_error = '';
    if (isset($_POST['custom_login_submit'])) {
        // Check nonce for security
        if (isset($_POST['_wpnonce']) && !wp_verify_nonce($_POST['_wpnonce'], 'custom_login_nonce')) {
            $login_error = 'Nonce verification failed.';
        } else {
            $creds = array(
                'user_login'    => sanitize_text_field($_POST['username']),
                'user_password' => sanitize_text_field($_POST['password']),
                'remember'      => isset($_POST['rememberme']),
            );

            // Attempt login
            $user = wp_signon($creds, is_ssl());

            if (is_wp_error($user)) {
                // Capture error message
                $login_error = $user->get_error_message();
            } else {
                // Successful login actions
                wp_set_current_user($user->ID);
                wp_set_auth_cookie($user->ID, isset($_POST['rememberme']));
                do_action('wp_login', $user->user_login, $user);

                // Force a session refresh
                wp_get_current_user(); // Ensures session is recognized

                // Debugging: Check if the user is logged in immediately after login
                if (is_user_logged_in()) {
                    echo 'User logged in successfully!'; // Debugging line
                } else {
                    echo 'User login failed. Not recognized!'; // Debugging line
                }

                // Redirect to /shop after successful login
                wp_redirect(home_url('/shop'));
                exit;
            }
        }
    }


    // Display the login form
    ?>
    <form id="loginform" method="post" action="">
        <!-- Nonce Field for Security -->
        <?php wp_nonce_field('custom_login_nonce'); ?>

        <?php if (!empty($login_error)): ?>
            <div class="login-error" style="color: red; margin-bottom: 15px;">
                <?php echo $login_error; ?>
            </div>
        <?php else: ?>
            <div class="login-error" style="color: red; margin-bottom: 15px;">
                <?php // echo is_user_logged_in() ? 'User is logged in' : 'User is not logged in'; ?>
            </div>
        <?php endif; ?>
        <p class="login-username">
            <input type="text" name="username" id="username" required placeholder="Email / Username">
        </p>
        <p class="login-password">
            <input type="password" name="password" id="password" required placeholder="Password">
        </p>
        <p class="login-submit">
            <input type="submit" name="custom_login_submit" value="Login" />
        </p>
        <input type="hidden" name="redirect_to" id="redirect_to" value="/my-account">
        <input type="hidden" name="redirect" id="redirect" value="/my-account">
        <p>
            <?php echo add_lost_password_link(); ?>
        </p>
    </form>
    <?php

    // Return the buffered content
    return ob_get_clean();
}
add_shortcode('custom_login', 'custom_login_form_shortcode');
 */

 // Handle AJAX login request
function handle_ajax_login() {
    // Check the nonce for security
    if ( isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'custom_login_nonce') ) {
        wp_send_json_error(array('message' => 'Nonce verification failed.'));
    }

    // Get the username and password from POST
    $creds = array(
        'user_login'    => sanitize_text_field($_POST['username']),
        'user_password' => sanitize_text_field($_POST['password']),
        'remember'      => isset($_POST['rememberme']),
    );

    // Attempt login
    $user = wp_signon($creds, is_ssl());

    if (is_wp_error($user)) {
        // Return error message if login failed
        wp_send_json_error(array('message' => $user->get_error_message()));
    } else {
        // Successful login actions
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, isset($_POST['rememberme']));
        do_action('wp_login', $user->user_login, $user);

        // Return success with redirection URL
        wp_send_json_success(array('redirect' => home_url('/shop')));
    }

    // End AJAX request
    wp_die();
}
add_action('wp_ajax_nopriv_ajax_login', 'handle_ajax_login');  // For non-logged-in users
add_action('wp_ajax_ajax_login', 'handle_ajax_login');         // For logged-in users

function custom_login_form_shortcode($atts) {
    // Start output buffering
    ob_start();

    // Display the login form
    ?>
    <form id="loginform" method="post" action="">
        <div id="login-error" style="color: red; margin-bottom: 15px;"></div>
        <!-- Nonce Field for Security -->
        <?php wp_nonce_field('custom_login_nonce'); ?>

        <p class="login-username">
            <input type="text" name="username" id="username" required placeholder="Email / Username">
        </p>
        <p class="login-password">
            <input type="password" name="password" id="password" required placeholder="Password">
        </p>
        <p class="login-submit">
            <button type="submit" id="login-submit" name="custom_login_submit">Login</button>
        </p>
        <input type="hidden" name="redirect_to" id="redirect_to" value="/my-account">
        <input type="hidden" name="redirect" id="redirect" value="/my-account">
        <p>
            <?php echo add_lost_password_link(); ?>
        </p>
    </form>

    <?php
    // Return the buffered content
    return ob_get_clean();
}
add_shortcode('custom_login', 'custom_login_form_shortcode');

function enqueue_custom_login_script() {
    $ver = filemtime(get_stylesheet_directory() . '/assets/js/custom-login-ajax.js');
    wp_enqueue_script('custom-login-ajax', get_stylesheet_directory_uri() . '/assets/js/custom-login-ajax.js?v=' . $ver, array('jquery'), null, true);

    // Localize script to provide nonce and AJAX URL
    wp_localize_script('custom-login-ajax', 'login_ajax_obj', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('custom_login_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_custom_login_script');

//--------------------------------------------------------

// // Add a new endpoint for company_accounts in My Account
// function add_company_accounts_endpoint() {
//     // Register the new endpoint for company_accounts, making it accessible via URL
//     add_rewrite_endpoint('company-accounts', EP_ROOT | EP_PAGES);
//     add_rewrite_endpoint('company-accounts/view', EP_ROOT | EP_PAGES);
//     add_rewrite_endpoint('company-accounts/edit', EP_ROOT | EP_PAGES);
// }
// add_action('init', 'add_company_accounts_endpoint');

// // Flush rewrite rules to ensure new endpoints are registered
// function flush_company_accounts_rewrite_rules() {
//     add_company_accounts_endpoint();
//     flush_rewrite_rules();
// }
// register_activation_hook(__FILE__, 'flush_company_accounts_rewrite_rules');
// register_deactivation_hook(__FILE__, 'flush_rewrite_rules');

// // Add the new query var so that WordPress recognizes it
// function company_accounts_query_vars($vars) {
//     $vars[] = 'company-accounts';
//     $vars[] = 'view_list';
//     $vars[] = 'edit_list';
//     return $vars;
// }
// add_filter('query_vars', 'company_accounts_query_vars');

// // Add the company accounts link to the My Account menu
// function add_company_accounts_link_my_account($items) {
//     $items['company-accounts'] = __('Company Accounts', 'woocommerce');
//     return $items;
// }
// add_filter('woocommerce_account_menu_items', 'add_company_accounts_link_my_account');


// function allow_backorder_without_stock_check($is_purchasable, $product) {
//     if ($product->get_stock_quantity() <= 0 && $product->get_backorders() === 'no') {
//         $is_purchasable = true;
//     }
//     return $is_purchasable;
// }
// add_filter('woocommerce_is_purchasable', 'allow_backorder_without_stock_check', 10, 2);



// function allow_more_than_stock_in_cart($passed, $product_id, $quantity) {
//     $product = wc_get_product($product_id);
//     if ($product->get_stock_quantity() < $quantity && $product->get_backorders() === 'yes') {
//         $passed = true;
//     }
//     return $passed;
// }
// add_filter('woocommerce_add_to_cart_validation', 'allow_more_than_stock_in_cart', 10, 3);


// add_filter('woocommerce_add_to_cart_validation', function ($passed, $product_id, $quantity) {
//     // Bypass stock validation
//     return true;
// }, 10, 3);

// add_filter('woocommerce_is_sold_individually', function ($return, $product) {
//     return false; // Allow multiple quantities for all products
// }, 10, 2);

// Debug file upload during checkout process
// Debug file upload during checkout process
function custom_debug_file_upload() {
    if (isset($_FILES['additional_files']) && !empty($_FILES['additional_files']['name'])) {
        $file = $_FILES['additional_files'];

        // Log file details to debug.log
        error_log('File upload attempt: ' . print_r($file, true));

        // Check if file was moved successfully
        $uploaded_file_path = wp_upload_dir()['path'] . '/' . $file['name'];
        if (move_uploaded_file($file['tmp_name'], $uploaded_file_path)) {
            error_log('File successfully uploaded to: ' . $uploaded_file_path);
        } else {
            error_log('File upload failed!');
        }
    } else {
        error_log('No file uploaded.');
    }
}
add_action('woocommerce_checkout_process', 'custom_debug_file_upload');

add_shortcode('file_upload_test', 'custom_file_upload_test_form');
function custom_file_upload_test_form() {
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['test_file'])) {
        $file = $_FILES['test_file'];
        $upload = wp_upload_bits($file['name'], null, file_get_contents($file['tmp_name']));

        if (empty($upload['error'])) {
            echo '<p>File uploaded successfully: <a href="' . esc_url($upload['url']) . '" target="_blank">View File</a></p>';
        } else {
            echo '<p>File upload error: ' . esc_html($upload['error']) . '</p>';
        }
    }
    ?>
        <form method="POST" enctype="multipart/form-data">
            <div class="form-row-row" style="margin: 100px 0 50px">
                <p class="form-row form-row-wide form-row-half">
                    <label for="additional_files">Upload Additional Files</label>
                    <input type="file" name="test_file" required accept=".xlsx, .xls, .pdf, .csv, .doc, .docx, .ppt, .pptx">
                </p>
            </div>
            <button type="submit">Upload Test File</button>
        </form>
    <?php
}

// Handle the file upload in WooCommerce checkout
function custom_handle_file_upload_on_checkout($posted_data) {
    if (isset($_FILES['additional_files']) && !empty($_FILES['additional_files']['name'])) {
        $file = $_FILES['additional_files'];

        // Upload the file to WordPress uploads directory
        $upload = wp_upload_bits($file['name'], null, file_get_contents($file['tmp_name']));

        if (!$upload['error']) {
            // Store the file URL in order meta
            WC()->session->set('uploaded_file_url', $upload['url']);
        } else {
            wc_add_notice(__('File upload failed: ', 'woocommerce') . $upload['error'], 'error');
        }
    }
}
add_action('woocommerce_checkout_process', 'custom_handle_file_upload_on_checkout');

// Save uploaded file URL to order meta
function custom_save_uploaded_file_to_order($order_id) {
    if (WC()->session->get('uploaded_file_url')) {
        $file_url = WC()->session->get('uploaded_file_url');
        update_post_meta($order_id, '_additional_files', $file_url);
        WC()->session->__unset('uploaded_file_url'); // Clear session data
    }
}
add_action('woocommerce_checkout_update_order_meta', 'custom_save_uploaded_file_to_order');

add_filter('woocommerce_billing_fields', 'make_billing_phone_optional');
function make_billing_phone_optional($fields) {
    if (isset($fields['billing_phone'])) {
        $fields['billing_phone']['required'] = false; // Set the phone field to not required
    }
    return $fields;
}

add_filter( 'woocommerce_checkout_fields', 'custom_shipping_name_optional' );

function custom_shipping_name_optional( $fields ) {
    // Set shipping first name optional
    if ( isset( $fields['shipping']['shipping_first_name'] ) ) {
        $fields['shipping']['shipping_first_name']['required'] = false;
    }

    // Set shipping last name optional
    if ( isset( $fields['shipping']['shipping_last_name'] ) ) {
        $fields['shipping']['shipping_last_name']['required'] = false;
    }

    return $fields;
}


add_action('admin_head', 'my_custom_fonts');

function my_custom_fonts() {
echo '<style>
.loadingg .select2::before {
    content: "";
    position: absolute;
    top: 50%;
    left: calc(100% + 20px);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 3px solid #2271b1;
    margin-top: -12px;
    border-top: ;
    border-top-color: transparent;
    animation: spin 1s infinite linear;
} 
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>';
}

/**
 * Add JavaScript validation for date picker to prevent past dates
 */
function hytec_add_date_picker_validation() {
    if (is_checkout()) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Set minimum date for the ship date field
            function setMinDate() {
                var today = new Date().toISOString().split('T')[0];
                $('#customer_requested_ship_date').attr('min', today);

                // Also validate on change
                $('#customer_requested_ship_date').on('change', function() {
                    var selectedDate = $(this).val();
                    var today = new Date().toISOString().split('T')[0];

                    if (selectedDate && selectedDate < today) {
                        alert('Please select a date that is today or in the future.');
                        $(this).val(''); // Clear the invalid date
                        $(this).focus();
                    }
                });
            }

            // Set min date on page load
            setMinDate();

            // Re-apply when checkout updates
            $(document.body).on('updated_checkout', function() {
                setTimeout(setMinDate, 100);
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'hytec_add_date_picker_validation');
